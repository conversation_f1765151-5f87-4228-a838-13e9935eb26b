"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6782],{35974:function(e,t,r){r.d(t,{C:function(){return i}});var n=r(57437);r(2265);var a=r(90535),o=r(94508);let s=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{className:(0,o.cn)(s({variant:r}),t),...a})}},62869:function(e,t,r){r.d(t,{z:function(){return c}});var n=r(57437),a=r(2265),o=r(37053),s=r(90535),i=r(94508);let d=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:s,asChild:c=!1,...u}=e,l=c?o.g7:"button";return(0,n.jsx)(l,{className:(0,i.cn)(d({variant:a,size:s,className:r})),ref:t,...u})});c.displayName="Button"},66070:function(e,t,r){r.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return s},aY:function(){return u},ll:function(){return d}});var n=r(57437),a=r(2265),o=r(94508);let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});s.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...a})});u.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},95186:function(e,t,r){r.d(t,{I:function(){return s}});var n=r(57437),a=r(2265),o=r(94508);let s=a.forwardRef((e,t)=>{let{className:r,type:a,...s}=e;return(0,n.jsx)("input",{type:a,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});s.displayName="Input"},53647:function(e,t,r){r.d(t,{Bw:function(){return g},Ph:function(){return u},Ql:function(){return y},i4:function(){return f},ki:function(){return l}});var n=r(57437),a=r(2265),o=r(57864),s=r(40875),i=r(22135),d=r(30401),c=r(94508);let u=o.fC;o.ZA;let l=o.B4,f=a.forwardRef((e,t)=>{let{className:r,children:a,...i}=e;return(0,n.jsxs)(o.xz,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...i,children:[a,(0,n.jsx)(o.JO,{asChild:!0,children:(0,n.jsx)(s.Z,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=o.xz.displayName;let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,n.jsx)(i.Z,{className:"h-4 w-4"})})});p.displayName=o.u_.displayName;let m=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,n.jsx)(s.Z,{className:"h-4 w-4"})})});m.displayName=o.$G.displayName;let g=a.forwardRef((e,t)=>{let{className:r,children:a,position:s="popper",...i}=e;return(0,n.jsx)(o.h_,{children:(0,n.jsxs)(o.VY,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:s,...i,children:[(0,n.jsx)(p,{}),(0,n.jsx)(o.l_,{className:(0,c.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,n.jsx)(m,{})]})})});g.displayName=o.VY.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=o.__.displayName;let y=a.forwardRef((e,t)=>{let{className:r,children:a,...s}=e;return(0,n.jsxs)(o.ck,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...s,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(o.wU,{children:(0,n.jsx)(d.Z,{className:"h-4 w-4"})})}),(0,n.jsx)(o.eT,{children:a})]})});y.displayName=o.ck.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=o.Z0.displayName},73578:function(e,t,r){r.d(t,{RM:function(){return d},SC:function(){return c},iA:function(){return s},pj:function(){return l},ss:function(){return u},xD:function(){return i}});var n=r(57437),a=r(2265),o=r(94508);let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,o.cn)("w-full caption-bottom text-sm",r),...a})})});s.displayName="Table";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("thead",{ref:t,className:(0,o.cn)("[&_tr]:border-b",r),...a})});i.displayName="TableHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,o.cn)("[&_tr:last-child]:border-0",r),...a})});d.displayName="TableBody",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tr",{ref:t,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});c.displayName="TableRow";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("th",{ref:t,className:(0,o.cn)("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});u.displayName="TableHead";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("td",{ref:t,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});l.displayName="TableCell",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("caption",{ref:t,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},29116:function(e,t,r){r.d(t,{Sb:function(){return i},Xy:function(){return s},kv:function(){return a},zg:function(){return o}});let n=r(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let a={register:e=>n.post("/auth/register",e),login:e=>n.post("/auth/login",e),logout:()=>n.post("/auth/logout"),verifyEmail:e=>n.post("/auth/verify-email",{token:e}),resendVerification:e=>n.post("/auth/resend-verification",{email:e}),forgotPassword:e=>n.post("/auth/forgot-password",{email:e}),resetPassword:e=>n.post("/auth/reset-password",e)},o={getAll:()=>n.get("/auctions"),getById:e=>n.get("/auctions/".concat(e)),create:e=>n.post("/auctions",e),update:(e,t)=>n.put("/auctions/".concat(e),t),delete:e=>n.delete("/auctions/".concat(e)),placeBid:(e,t)=>n.post("/auctions/".concat(e,"/bid"),{amount:t})},s={getFavorites:e=>n.get("/favorites",{params:e}),addFavorite:e=>n.post("/favorites",e),removeFavorite:(e,t)=>n.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,r)=>n.put("/favorites/".concat(e,"/").concat(t),r),checkFavorite:(e,t)=>n.get("/favorites/check/".concat(e,"/").concat(t))},i={getDashboardStats:()=>n.get("/admin/dashboard"),getPendingAccounts:()=>n.get("/admin/pending-accounts"),approvePendingAccount:e=>n.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>n.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>n.get("/admin/users",{params:e}),getById:e=>n.get("/admin/users/".concat(e)),update:(e,t)=>n.put("/admin/users/".concat(e),t),delete:e=>n.delete("/admin/users/".concat(e)),activate:e=>n.post("/admin/users/".concat(e,"/activate")),deactivate:e=>n.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>n.get("/admin/auctions",{params:e}),getById:e=>n.get("/admin/auctions/".concat(e)),approve:e=>n.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>n.get("/admin/tenders",{params:e}),getById:e=>n.get("/admin/tenders/".concat(e)),approve:e=>n.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/tenders/".concat(e))},getTender:e=>n.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>n.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>n.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,r)=>n.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),r),reports:{getFinancialReport:e=>n.get("/admin/reports/financial",{params:e}),getUserReport:e=>n.get("/admin/reports/users",{params:e}),getActivityReport:e=>n.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>n.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>n.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>n.get("/admin/settings"),update:e=>n.put("/admin/settings",e),backup:()=>n.post("/admin/settings/backup"),restore:e=>n.post("/admin/settings/restore/".concat(e))}};t.ZP=n},94508:function(e,t,r){r.d(t,{cn:function(){return o}});var n=r(61994),a=r(53335);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}},95252:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},42208:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},90740:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},60285:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("PlusCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},73247:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},18930:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},95805:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},99376:function(e,t,r){var n=r(35475);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})}}]);