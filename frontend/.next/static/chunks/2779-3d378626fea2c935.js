"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2779],{94766:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},60044:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},92451:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},10407:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},98617:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},95252:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},42208:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25466:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},47692:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},48664:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},60285:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("PlusCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},98728:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88906:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},49474:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},92369:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5481:function(e,t,n){n.d(t,{T:function(){return o}});var r=n(2265),a=n(31057),i=n(43841),l=["axis"],o=(0,r.forwardRef)((e,t)=>r.createElement(i.R,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:a.NL,categoricalChartProps:e,ref:t}))},41718:function(e,t,n){n.d(t,{u:function(){return c}});var r=n(2265),a=n(31057),i=n(88077),l=n(40130),o=["item"],u={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},c=(0,r.forwardRef)((e,t)=>{var n=(0,l.j)(e,u);return r.createElement(i.h,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:o,tooltipPayloadSearcher:a.NL,categoricalChartProps:n,ref:t})})},68069:function(e,t,n){n.d(t,{b:function(){return eu},w:function(){return en}});var r=n(2265),a=n(15870),i=n.n(a),l=n(61994),o=n(92713),u=n(22932),c=n(74653),d=n(49037),s=n(11251);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach(function(t){var r,a;r=t,a=n[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var f=(e,t)=>t,h=[],g=(e,t,n)=>(null==n?void 0:n.length)===0?h:n,m=(0,o.P1)([u.RV,f,g],(e,t,n)=>{var r,{chartData:a}=e;if((r=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:a)&&r.length||null==n||(r=n.map(e=>y(y({},t.presentationProps),e.props))),null!=r)return r}),v=(0,o.P1)([m,f,g],(e,t,n)=>{if(null!=e)return e.map((e,r)=>{var a,i,l=(0,d.F$)(e,t.nameKey,t.name);return i=null!=n&&null!==(a=n[r])&&void 0!==a&&null!==(a=a.props)&&void 0!==a&&a.fill?n[r].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,d.hn)(l,t.dataKey),color:i,payload:e,type:t.legendType}})}),b=(0,o.P1)([s.vE,f],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),k=(0,o.P1)([m,b,g,c.DX],(e,t,n,r)=>{if(null!=t&&null!=e)return en({offset:r,pieSettings:t,displayedData:e,cells:n})}),A=n(39040),x=n(13790),E=n(9841),O=n(57165),P=n(58811),w=n(20407),M=n(82944),R=n(34067),j=n(39206),Z=n(16630),T=n(41637),S=n(11638),C=n(44296),K=n(35623),N=n(31944),L=n(62658),D=n(78487),V=n(59087),z=n(40130),F=n(46595),H=["onMouseEnter","onClick","onMouseLeave"];function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function I(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach(function(t){$(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function $(e,t,n){var r;return(t="symbol"==typeof(r=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?r:r+"")in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(){return(B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}function Y(e){var t=(0,r.useMemo)(()=>(0,M.L6)(e,!1),[e]),n=(0,r.useMemo)(()=>(0,M.NN)(e.children,w.b),[e.children]),a=(0,r.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=(0,A.C)(e=>v(e,a,n));return r.createElement(L.t,{legendPayload:i})}function _(e){var{dataKey:t,nameKey:n,sectors:r,stroke:a,strokeWidth:i,fill:l,name:o,hide:u,tooltipType:c}=e;return{dataDefinedOnItem:null==r?void 0:r.map(e=>e.tooltipPayload),positions:null==r?void 0:r.map(e=>e.tooltipPosition),settings:{stroke:a,strokeWidth:i,fill:l,dataKey:t,nameKey:n,name:(0,d.hn)(o,t),hide:u,type:c,color:l,unit:""}}}var W=(e,t)=>e>t?"start":e<t?"end":"middle",G=(e,t,n)=>"function"==typeof t?t(e):(0,Z.h1)(t,n,.8*n),Q=(e,t,n)=>{var{top:r,left:a,width:i,height:l}=t,o=(0,j.$4)(i,l),u=a+(0,Z.h1)(e.cx,i,i/2),c=r+(0,Z.h1)(e.cy,l,l/2);return{cx:u,cy:c,innerRadius:(0,Z.h1)(e.innerRadius,o,0),outerRadius:G(n,e.outerRadius,o),maxRadius:e.maxRadius||Math.sqrt(i*i+l*l)/2}},U=(e,t)=>(0,Z.uY)(t-e)*Math.min(Math.abs(t-e),360),X=(e,t)=>{if(r.isValidElement(e))return r.cloneElement(e,t);if("function"==typeof e)return e(t);var n=(0,l.W)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return r.createElement(O.H,B({},t,{type:"linear",className:n}))},J=(e,t,n)=>{if(r.isValidElement(e))return r.cloneElement(e,t);var a=n;if("function"==typeof e&&(a=e(t),r.isValidElement(a)))return a;var i=(0,l.W)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return r.createElement(P.x,B({},t,{alignmentBaseline:"middle",className:i}),a)};function ee(e){var{sectors:t,props:n,showLabels:a}=e,{label:i,labelLine:l,dataKey:o}=n;if(!a||!i||!t)return null;var u=(0,M.L6)(n,!1),c=(0,M.L6)(i,!1),s=(0,M.L6)(l,!1),p="object"==typeof i&&"offsetRadius"in i&&i.offsetRadius||20,y=t.map((e,t)=>{var n=(e.startAngle+e.endAngle)/2,a=(0,j.op)(e.cx,e.cy,e.outerRadius+p,n),y=I(I(I(I({},u),e),{},{stroke:"none"},c),{},{index:t,textAnchor:W(a.x,e.cx)},a),f=I(I(I(I({},u),e),{},{fill:"none",stroke:e.fill},s),{},{index:t,points:[(0,j.op)(e.cx,e.cy,e.outerRadius,n),a],key:"line"});return r.createElement(E.m,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},l&&X(l,f),J(i,y,(0,d.F$)(e,o)))});return r.createElement(E.m,{className:"recharts-pie-labels"},y)}function et(e){var{sectors:t,activeShape:n,inactiveShape:a,allOtherPieProps:i,showLabels:l}=e,o=(0,A.C)(N.Ve),{onMouseEnter:u,onClick:c,onMouseLeave:d}=i,s=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&({}).propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(i,H),p=(0,C.Df)(u,i.dataKey),y=(0,C.oQ)(d),f=(0,C.nC)(c,i.dataKey);return null==t?null:r.createElement(r.Fragment,null,t.map((e,l)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var u=n&&String(l)===o,c=u?n:o?a:null,d=I(I({},e),{},{stroke:e.stroke,tabIndex:-1,[D.Gh]:l,[D.aN]:i.dataKey});return r.createElement(E.m,B({tabIndex:-1,className:"recharts-pie-sector"},(0,T.bw)(s,e,l),{onMouseEnter:p(e,l),onMouseLeave:y(e,l),onClick:f(e,l),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(l)}),r.createElement(S.b,B({option:c,isActive:u,shapeType:"sector"},d)))}),r.createElement(ee,{sectors:t,props:i,showLabels:l}))}function en(e){var t,n,r,{pieSettings:a,displayedData:i,cells:l,offset:o}=e,{cornerRadius:u,startAngle:c,endAngle:s,dataKey:p,nameKey:y,tooltipType:f}=a,h=Math.abs(a.minAngle),g=U(c,s),m=Math.abs(g),v=i.length<=1?0:null!==(t=a.paddingAngle)&&void 0!==t?t:0,b=i.filter(e=>0!==(0,d.F$)(e,p,0)).length,k=m-b*h-(m>=360?b:b-1)*v,A=i.reduce((e,t)=>{var n=(0,d.F$)(t,p,0);return e+((0,Z.hj)(n)?n:0)},0);return A>0&&(n=i.map((e,t)=>{var n,i=(0,d.F$)(e,p,0),s=(0,d.F$)(e,y,t),m=Q(a,o,e),b=((0,Z.hj)(i)?i:0)/A,x=I(I({},e),l&&l[t]&&l[t].props),E=(n=t?r.endAngle+(0,Z.uY)(g)*v*(0!==i?1:0):c)+(0,Z.uY)(g)*((0!==i?h:0)+b*k),O=(n+E)/2,P=(m.innerRadius+m.outerRadius)/2,w=[{name:s,value:i,payload:x,dataKey:p,type:f}],M=(0,j.op)(m.cx,m.cy,P,O);return r=I(I(I(I({},a.presentationProps),{},{percent:b,cornerRadius:u,name:s,tooltipPayload:w,midAngle:O,middleRadius:P,tooltipPosition:M},x),m),{},{value:(0,d.F$)(e,p),startAngle:n,endAngle:E,payload:x,paddingAngle:(0,Z.uY)(g)*v})})),n}function er(e){var{props:t,previousSectorsRef:n}=e,{sectors:a,isAnimationActive:l,animationBegin:o,animationDuration:u,animationEasing:c,activeShape:d,inactiveShape:s,onAnimationStart:p,onAnimationEnd:y}=t,f=(0,V.i)(t,"recharts-pie-"),h=n.current,[g,m]=(0,r.useState)(!0),v=(0,r.useCallback)(()=>{"function"==typeof y&&y(),m(!1)},[y]),b=(0,r.useCallback)(()=>{"function"==typeof p&&p(),m(!0)},[p]);return r.createElement(F.r,{begin:o,duration:u,isActive:l,easing:c,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:v,key:f},e=>{var{t:l}=e,o=[],u=(a&&a[0]).startAngle;return a.forEach((e,t)=>{var n=h&&h[t],r=t>0?i()(e,"paddingAngle",0):0;if(n){var a=(0,Z.k4)(n.endAngle-n.startAngle,e.endAngle-e.startAngle),c=I(I({},e),{},{startAngle:u+r,endAngle:u+a(l)+r});o.push(c),u=c.endAngle}else{var{endAngle:d,startAngle:s}=e,p=(0,Z.k4)(0,d-s)(l),y=I(I({},e),{},{startAngle:u+r,endAngle:u+p+r});o.push(y),u=y.endAngle}}),n.current=o,r.createElement(E.m,null,r.createElement(et,{sectors:o,activeShape:d,inactiveShape:s,allOtherPieProps:t,showLabels:!g}))})}function ea(e){var{sectors:t,isAnimationActive:n,activeShape:a,inactiveShape:i}=e,l=(0,r.useRef)(null),o=l.current;return n&&t&&t.length&&(!o||o!==t)?r.createElement(er,{props:e,previousSectorsRef:l}):r.createElement(et,{sectors:t,activeShape:a,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function ei(e){var{hide:t,className:n,rootTabIndex:a}=e,i=(0,l.W)("recharts-pie",n);return t?null:r.createElement(E.m,{tabIndex:a,className:i},r.createElement(ea,e))}var el={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!R.x.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function eo(e){var t=(0,z.j)(e,el),n=(0,r.useMemo)(()=>(0,M.NN)(e.children,w.b),[e.children]),a=(0,M.L6)(t,!1),i=(0,r.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:a}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,a]),l=(0,A.C)(e=>k(e,i,n));return r.createElement(r.Fragment,null,r.createElement(K.k,{fn:_,args:I(I({},t),{},{sectors:l})}),r.createElement(ei,B({},t,{sectors:l})))}class eu extends r.PureComponent{render(){return r.createElement(r.Fragment,null,r.createElement(x.E,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),r.createElement(Y,this.props),r.createElement(eo,this.props),this.props.children)}constructor(){super(...arguments),$(this,"id",(0,Z.EL)("recharts-pie-"))}}$(eu,"displayName","Pie"),$(eu,"defaultProps",el)}}]);