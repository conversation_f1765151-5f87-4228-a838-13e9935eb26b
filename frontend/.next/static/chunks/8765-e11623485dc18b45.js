"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8765],{42488:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},33327:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},31047:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},98617:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},66142:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]])},82431:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},86595:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},58896:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},70525:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},49474:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},92369:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},24369:function(e,t,n){var r=n(2265),u="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,o=r.useEffect,l=r.useLayoutEffect,a=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!u(e,n)}catch(e){return!0}}var f="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),u=r[0].inst,f=r[1];return l(function(){u.value=n,u.getSnapshot=t,c(u)&&f({inst:u})},[e,n,t]),o(function(){return c(u)&&f({inst:u}),e(function(){c(u)&&f({inst:u})})},[e]),a(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:f},82558:function(e,t,n){e.exports=n(24369)},61146:function(e,t,n){n.d(t,{NY:function(){return Z},Ee:function(){return S},fC:function(){return b}});var r=n(2265),u=n(73966),i=n(26606),o=n(61188),l=n(66840),a=n(82558);function c(){return()=>{}}var f=n(57437),d="Avatar",[s,p]=(0,u.b)(d),[y,v]=s(d),h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...u}=e,[i,o]=r.useState("idle");return(0,f.jsx)(y,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:o,children:(0,f.jsx)(l.WV.span,{...u,ref:t})})});h.displayName=d;var m="AvatarImage",k=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:u,onLoadingStatusChange:d=()=>{},...s}=e,p=v(m,n),y=function(e,t){let{referrerPolicy:n,crossOrigin:u}=t,i=(0,a.useSyncExternalStore)(c,()=>!0,()=>!1),l=r.useRef(null),f=i?(l.current||(l.current=new window.Image),l.current):null,[d,s]=r.useState(()=>x(f,e));return(0,o.b)(()=>{s(x(f,e))},[f,e]),(0,o.b)(()=>{let e=e=>()=>{s(e)};if(!f)return;let t=e("loaded"),r=e("error");return f.addEventListener("load",t),f.addEventListener("error",r),n&&(f.referrerPolicy=n),"string"==typeof u&&(f.crossOrigin=u),()=>{f.removeEventListener("load",t),f.removeEventListener("error",r)}},[f,u,n]),d}(u,s),h=(0,i.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,o.b)(()=>{"idle"!==y&&h(y)},[y,h]),"loaded"===y?(0,f.jsx)(l.WV.img,{...s,ref:t,src:u}):null});k.displayName=m;var w="AvatarFallback",g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:u,...i}=e,o=v(w,n),[a,c]=r.useState(void 0===u);return r.useEffect(()=>{if(void 0!==u){let e=window.setTimeout(()=>c(!0),u);return()=>window.clearTimeout(e)}},[u]),a&&"loaded"!==o.imageLoadingStatus?(0,f.jsx)(l.WV.span,{...i,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}g.displayName=w;var b=h,S=k,Z=g},98575:function(e,t,n){n.d(t,{F:function(){return i},e:function(){return o}});var r=n(2265);function u(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=u(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():u(e[t],null)}}}}function o(...e){return r.useCallback(i(...e),e)}},73966:function(e,t,n){n.d(t,{b:function(){return i}});var r=n(2265),u=n(57437);function i(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let u=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:u}}),[n,u])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),l=n.length;n=[...n,i];let a=t=>{let{scope:n,children:i,...a}=t,c=n?.[e]?.[l]||o,f=r.useMemo(()=>a,Object.values(a));return(0,u.jsx)(c.Provider,{value:f,children:i})};return a.displayName=t+"Provider",[a,function(n,u){let a=u?.[e]?.[l]||o,c=r.useContext(a);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let u=n.reduce((t,{useScope:n,scopeName:r})=>{let u=n(e)[`__scope${r}`];return{...t,...u}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:u}),[u])}};return n.scopeName=t.scopeName,n}(i,...t)]}},66840:function(e,t,n){n.d(t,{WV:function(){return l},jH:function(){return a}});var r=n(2265),u=n(54887),i=n(37053),o=n(57437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.Z8)(`Primitive.${t}`),u=r.forwardRef((e,r)=>{let{asChild:u,...i}=e,l=u?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l,{...i,ref:r})});return u.displayName=`Primitive.${t}`,{...e,[t]:u}},{});function a(e,t){e&&u.flushSync(()=>e.dispatchEvent(t))}},37053:function(e,t,n){n.d(t,{Z8:function(){return o},g7:function(){return l}});var r=n(2265),u=n(98575),i=n(57437);function o(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){let e,o;let l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let u=e[r],i=t[r];/^on[A-Z]/.test(r)?u&&i?n[r]=(...e)=>{let t=i(...e);return u(...e),t}:u&&(n[r]=u):"style"===r?n[r]={...u,...i}:"className"===r&&(n[r]=[u,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(a.ref=t?(0,u.F)(t,l):l),r.cloneElement(n,a)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:u,...o}=e,l=r.Children.toArray(u),a=l.find(c);if(a){let e=a.props.children,u=l.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,u):null})}return(0,i.jsx)(t,{...o,ref:n,children:u})});return n.displayName=`${e}.Slot`,n}var l=o("Slot"),a=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},26606:function(e,t,n){n.d(t,{W:function(){return u}});var r=n(2265);function u(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},61188:function(e,t,n){n.d(t,{b:function(){return u}});var r=n(2265),u=globalThis?.document?r.useLayoutEffect:()=>{}},90535:function(e,t,n){n.d(t,{j:function(){return o}});var r=n(61994);let u=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.W,o=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:l}=t,a=Object.keys(o).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let i=u(t)||u(r);return o[e][i]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,a,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...u}=t;return Object.entries(u).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...c}[t]):({...l,...c})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}}]);