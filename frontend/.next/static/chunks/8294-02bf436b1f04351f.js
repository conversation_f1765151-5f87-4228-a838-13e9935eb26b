"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8294],{17580:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},58896:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},52797:function(e,t,r){r.d(t,{x:function(){return er},r:function(){return et}});var n=r(2265),a=r(61994),i=r(57165),l=r(81889),o=r(9841),s=r(58772),c=r(13137),u=r(16630),p=r(82944),d=r(34067),f=r(49037),y=r(54710),m=r(35623),v=r(87279),h=r(45702),b=r(35953),g=r(58735),x=r(92713),P=r(22932),E=r(98628),O=(e,t,r,n)=>(0,E.AS)(e,"xAxis",t,n),A=(e,t,r,n)=>(0,E.bY)(e,"xAxis",t,n),w=(e,t,r,n)=>(0,E.AS)(e,"yAxis",r,n),j=(e,t,r,n)=>(0,E.bY)(e,"yAxis",r,n),k=(0,x.P1)([b.rE,O,w,A,j],(e,t,r,n,a)=>(0,f.NA)(e,"xAxis")?(0,f.zT)(t,n,!1):(0,f.zT)(r,a,!1)),I=(0,x.P1)([E.bm,(e,t,r,n,a)=>a],(e,t)=>{if(e.some(e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),S=(0,x.P1)([b.rE,O,w,A,j,I,k,P.hA],(e,t,r,n,a,i,l,o)=>{var s,{chartData:c,dataStartIndex:u,dataEndIndex:p}=o;if(null!=i&&null!=t&&null!=r&&null!=n&&null!=a&&0!==n.length&&0!==a.length&&null!=l){var{dataKey:d,data:f}=i;if(null!=(s=null!=f&&f.length>0?f:null==c?void 0:c.slice(u,p+1)))return et({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:a,dataKey:d,bandSize:l,displayedData:s})}}),K=r(39040),T=r(62658),N=r(59087),C=r(40130),D=r(46595),z=r(7986),R=["type","layout","connectNulls","needClip"],L=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function M(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){W(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function W(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var H=e=>{var{dataKey:t,name:r,stroke:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:t,type:a,color:n,value:(0,f.hn)(r,t),payload:e}]};function Y(e){var{dataKey:t,data:r,stroke:n,strokeWidth:a,fill:i,name:l,hide:o,unit:s}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:a,fill:i,dataKey:t,nameKey:void 0,name:(0,f.hn)(l,t),hide:o,type:e.tooltipType,color:e.stroke,unit:s}}}var $=(e,t)=>"".concat(t,"px ").concat(e-t,"px"),U=(e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return $(t,e);for(var a=e%n,i=t-e,l=[],o=0,s=0;o<r.length;s+=r[o],++o)if(s+r[o]>a){l=[...r.slice(0,o),a-s];break}var c=l.length%2==0?[0,i]:[i];return[...function(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],a=0;a<t;++a)n=[...n,...r];return n}(r,Math.floor(e/n)),...l,...c].map(e=>"".concat(e,"px")).join(", ")};function J(e){var{clipPathId:t,points:r,props:i}=e,{dot:s,dataKey:c,needClip:u}=i;if(null==r||!s&&1!==r.length)return null;var d=(0,p.W0)(s),f=(0,p.L6)(i,!1),y=(0,p.L6)(s,!0),m=r.map((e,t)=>(function(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var i=(0,a.W)("recharts-line-dot","boolean"!=typeof e?e.className:"");r=n.createElement(l.o,V({},t,{className:i}))}return r})(s,F(F(F({key:"dot-".concat(t),r:3},f),y),{},{index:t,cx:e.x,cy:e.y,dataKey:c,value:e.value,payload:e.payload,points:r}))),v={clipPath:u?"url(#clipPath-".concat(d?"":"dots-").concat(t,")"):null};return n.createElement(o.m,V({className:"recharts-line-dots",key:"dots"},v),m)}function _(e){var{clipPathId:t,pathRef:r,points:a,strokeDasharray:l,props:o,showLabels:c}=e,{type:u,layout:d,connectNulls:f,needClip:y}=o,m=M(o,R),v=F(F({},(0,p.L6)(m,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:y?"url(#clipPath-".concat(t,")"):null,points:a,type:u,layout:d,connectNulls:f,strokeDasharray:null!=l?l:o.strokeDasharray});return n.createElement(n.Fragment,null,(null==a?void 0:a.length)>1&&n.createElement(i.H,V({},v,{pathRef:r})),n.createElement(J,{points:a,clipPathId:t,props:o}),c&&s.e.renderCallByParent(o,a))}function Z(e){var{clipPathId:t,props:r,pathRef:a,previousPointsRef:i,longestAnimatedLengthRef:l}=e,{points:o,strokeDasharray:s,isAnimationActive:c,animationBegin:p,animationDuration:d,animationEasing:f,animateNewValues:y,width:m,height:v,onAnimationEnd:h,onAnimationStart:b}=r,g=i.current,x=(0,N.i)(r,"recharts-line-"),[P,E]=(0,n.useState)(!1),O=(0,n.useCallback)(()=>{"function"==typeof h&&h(),E(!1)},[h]),A=(0,n.useCallback)(()=>{"function"==typeof b&&b(),E(!0)},[b]),w=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(a.current),j=l.current;return n.createElement(D.r,{begin:p,duration:d,isActive:c,easing:f,from:{t:0},to:{t:1},onAnimationEnd:O,onAnimationStart:A,key:x},e=>{var c,{t:p}=e,d=Math.min((0,u.k4)(j,w+j)(p),w);if(c=s?U(d,w,"".concat(s).split(/[,\s]+/gim).map(e=>parseFloat(e))):$(w,d),g){var f=g.length/o.length,h=1===p?o:o.map((e,t)=>{var r=Math.floor(t*f);if(g[r]){var n=g[r],a=(0,u.k4)(n.x,e.x),i=(0,u.k4)(n.y,e.y);return F(F({},e),{},{x:a(p),y:i(p)})}if(y){var l=(0,u.k4)(2*m,e.x),o=(0,u.k4)(v/2,e.y);return F(F({},e),{},{x:l(p),y:o(p)})}return F(F({},e),{},{x:e.x,y:e.y})});return i.current=h,n.createElement(_,{props:r,points:h,clipPathId:t,pathRef:a,showLabels:!P,strokeDasharray:c})}return p>0&&w>0&&(i.current=o,l.current=d),n.createElement(_,{props:r,points:o,clipPathId:t,pathRef:a,showLabels:!P,strokeDasharray:c})})}function Q(e){var{clipPathId:t,props:r}=e,{points:a,isAnimationActive:i}=r,l=(0,n.useRef)(null),o=(0,n.useRef)(0),s=(0,n.useRef)(null),c=l.current;return i&&a&&a.length&&c!==a?n.createElement(Z,{props:r,clipPathId:t,previousPointsRef:l,longestAnimatedLengthRef:o,pathRef:s}):n.createElement(_,{props:r,points:a,clipPathId:t,pathRef:s,showLabels:!0})}var q=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:(0,f.F$)(e.payload,t)});class G extends n.Component{render(){var e,{hide:t,dot:r,points:i,className:l,xAxisId:s,yAxisId:d,top:f,left:m,width:b,height:g,id:x,needClip:P,layout:E}=this.props;if(t)return null;var O=(0,a.W)("recharts-line",l),A=(0,u.Rw)(x)?this.id:x,{r:w=3,strokeWidth:j=2}=null!==(e=(0,p.L6)(r,!1))&&void 0!==e?e:{r:3,strokeWidth:2},k=(0,p.W0)(r),I=2*w+j;return n.createElement(n.Fragment,null,n.createElement(o.m,{className:O},P&&n.createElement("defs",null,n.createElement(h.W,{clipPathId:A,xAxisId:s,yAxisId:d}),!k&&n.createElement("clipPath",{id:"clipPath-dots-".concat(A)},n.createElement("rect",{x:m-I/2,y:f-I/2,width:b+I,height:g+I}))),n.createElement(Q,{props:this.props,clipPathId:A}),n.createElement(c.D,{direction:"horizontal"===E?"y":"x"},n.createElement(v.zU,{xAxisId:s,yAxisId:d,data:i,dataPointFormatter:q,errorBarOffset:0},this.props.children))),n.createElement(y.j,{activeDot:this.props.activeDot,points:i,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}constructor(){super(...arguments),W(this,"id",(0,u.EL)("recharts-line-"))}}var X={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!d.x.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function ee(e){var t=(0,C.j)(e,X),{activeDot:r,animateNewValues:a,animationBegin:i,animationDuration:l,animationEasing:o,connectNulls:s,dot:c,hide:u,isAnimationActive:p,label:d,legendType:f,xAxisId:y,yAxisId:m}=t,v=M(t,L),{needClip:x}=(0,h.N)(y,m),{height:P,width:E,x:O,y:A}=(0,z.$$)(),w=(0,b.vn)(),j=(0,g.W)(),k=(0,n.useMemo)(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),I=(0,K.C)(e=>S(e,y,m,j,k));return"horizontal"!==w&&"vertical"!==w?null:n.createElement(G,V({},v,{connectNulls:s,dot:c,activeDot:r,animateNewValues:a,animationBegin:i,animationDuration:l,animationEasing:o,isAnimationActive:p,hide:u,label:d,legendType:f,xAxisId:y,yAxisId:m,points:I,layout:w,height:P,width:E,left:O,top:A,needClip:x}))}function et(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:a,yAxisTicks:i,dataKey:l,bandSize:o,displayedData:s}=e;return s.map((e,s)=>{var c=(0,f.F$)(e,l);return"horizontal"===t?{x:(0,f.Hv)({axis:r,ticks:a,bandSize:o,entry:e,index:s}),y:(0,u.Rw)(c)?null:n.scale(c),value:c,payload:e}:{x:(0,u.Rw)(c)?null:r.scale(c),y:(0,f.Hv)({axis:n,ticks:i,bandSize:o,entry:e,index:s}),value:c,payload:e}})}class er extends n.PureComponent{render(){return n.createElement(v.Ph,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},n.createElement(T.L,{legendPayload:H(this.props)}),n.createElement(m.k,{fn:Y,args:this.props}),n.createElement(ee,this.props))}}W(er,"displayName","Line"),W(er,"defaultProps",X)},94970:function(e,t,r){r.d(t,{b:function(){return ea},u:function(){return X}});var n=r(2265),a=r(61994),i=r(9841),l=r(58772),o=r(82944),s=r(34067),c=r(17644),u=r(39040),p=r(98628);function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(e){var t=(0,u.T)();return(0,n.useEffect)(()=>(t((0,c.CJ)(e)),()=>{t((0,c.ei)(e))}),[e,t]),null}class y extends n.Component{render(){return n.createElement(f,{domain:this.props.domain,id:this.props.zAxisId,dataKey:this.props.dataKey,name:this.props.name,unit:this.props.unit,range:this.props.range,scale:this.props.scale,type:this.props.type,allowDuplicatedCategory:p.s1.allowDuplicatedCategory,allowDataOverflow:p.s1.allowDataOverflow,reversed:p.s1.reversed,includeHidden:p.s1.includeHidden})}}d(y,"displayName","ZAxis"),d(y,"defaultProps",{zAxisId:0,range:p.s1.range,scale:p.s1.scale,type:p.s1.type});var m=r(57165),v=r(20407),h=r(16630),b=r(49037),g=r(41637),x=r(14870),P=r(11638),E=["option","isActive"];function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function A(e){var{option:t,isActive:r}=e,a=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,E);return"string"==typeof t?n.createElement(P.b,O({option:n.createElement(x.v,O({type:t},a)),isActive:r,shapeType:"symbols"},a)):n.createElement(P.b,O({option:t,isActive:r,shapeType:"symbols"},a))}var w=r(44296),j=r(35623),k=r(87279),I=r(45702),S=r(92713),K=r(22932),T=(0,S.P1)([p.bm,(e,t,r,n,a)=>a],(e,t)=>{if(e.some(e=>"scatter"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),N=(0,S.P1)([(e,t,r,n,a,i,l)=>(0,K.hA)(e,t,r,l),(e,t,r,n,a,i,l)=>(0,p.AS)(e,"xAxis",t,l),(e,t,r,n,a,i,l)=>(0,p.bY)(e,"xAxis",t,l),(e,t,r,n,a,i,l)=>(0,p.AS)(e,"yAxis",r,l),(e,t,r,n,a,i,l)=>(0,p.bY)(e,"yAxis",r,l),(e,t,r,n)=>(0,p.J4)(e,"zAxis",n,!1),T,(e,t,r,n,a,i)=>i],(e,t,r,n,a,i,l,o)=>{var s,{chartData:c,dataStartIndex:u,dataEndIndex:p}=e;if(null!=l&&null!=(s=(null==l?void 0:l.data)!=null&&l.data.length>0?l.data:null==c?void 0:c.slice(u,p+1))&&null!=t&&null!=n&&null!=r&&null!=a&&(null==r?void 0:r.length)!==0&&(null==a?void 0:a.length)!==0)return X({displayedData:s,xAxis:t,yAxis:n,zAxis:i,scatterSettings:l,xAxisTicks:r,yAxisTicks:a,cells:o})}),C=r(58735),D=r(31944),z=r(62658),R=r(78487),L=r(59087),M=r(40130),B=r(46595),F=["onMouseEnter","onClick","onMouseLeave"],W=["animationBegin","animationDuration","animationEasing","hide","isAnimationActive","legendType","lineJointType","lineType","shape","xAxisId","yAxisId","zAxisId"];function V(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function H(){return(H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(r),!0).forEach(function(t){U(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function U(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var J=e=>{var{dataKey:t,name:r,fill:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:t,type:a,color:n,value:(0,b.hn)(r,t),payload:e}]};function _(e){var t,r,{points:a,props:l}=e,{line:s,lineType:c,lineJointType:u}=l;if(!s)return null;var p=(0,o.L6)(l,!1),d=(0,o.L6)(s,!1);if("joint"===c)t=a.map(e=>({x:e.cx,y:e.cy}));else if("fitting"===c){var{xmin:f,xmax:y,a:v,b}=(0,h.wr)(a);t=[{x:f,y:v*f+b},{x:y,y:v*y+b}]}var g=$($($({},p),{},{fill:"none",stroke:p&&p.fill},d),{},{points:t});return r=n.isValidElement(s)?n.cloneElement(s,g):"function"==typeof s?s(g):n.createElement(m.H,H({},g,{type:u})),n.createElement(i.m,{className:"recharts-scatter-line",key:"recharts-scatter-line"},r)}function Z(e){var{points:t,showLabels:r,allOtherScatterProps:a}=e,{shape:s,activeShape:c,dataKey:p}=a,d=(0,o.L6)(a,!1),f=(0,u.C)(D.Ve),{onMouseEnter:y,onClick:m,onMouseLeave:v}=a,h=V(a,F),b=(0,w.Df)(y,a.dataKey),x=(0,w.oQ)(v),P=(0,w.nC)(m,a.dataKey);return null==t?null:n.createElement(n.Fragment,null,n.createElement(_,{points:t,props:a}),t.map((e,t)=>{var r=c&&f===String(t),a=r?c:s,l=$($($({key:"symbol-".concat(t)},d),e),{},{[R.Gh]:t,[R.aN]:String(p)});return n.createElement(i.m,H({className:"recharts-scatter-symbol"},(0,g.bw)(h,e,t),{onMouseEnter:b(e,t),onMouseLeave:x(e,t),onClick:P(e,t),key:"symbol-".concat(null==e?void 0:e.cx,"-").concat(null==e?void 0:e.cy,"-").concat(null==e?void 0:e.size,"-").concat(t)}),n.createElement(A,H({option:a,isActive:r},l)))}),r&&l.e.renderCallByParent(a,t))}function Q(e){var{previousPointsRef:t,props:r}=e,{points:a,isAnimationActive:l,animationBegin:o,animationDuration:s,animationEasing:c}=r,u=t.current,p=(0,L.i)(r,"recharts-scatter-"),[d,f]=(0,n.useState)(!1),y=(0,n.useCallback)(()=>{f(!1)},[]),m=(0,n.useCallback)(()=>{f(!0)},[]);return n.createElement(B.r,{begin:o,duration:s,isActive:l,easing:c,from:{t:0},to:{t:1},onAnimationEnd:y,onAnimationStart:m,key:p},e=>{var{t:l}=e,o=1===l?a:a.map((e,t)=>{var r=u&&u[t];if(r){var n=(0,h.k4)(r.cx,e.cx),a=(0,h.k4)(r.cy,e.cy),i=(0,h.k4)(r.size,e.size);return $($({},e),{},{cx:n(l),cy:a(l),size:i(l)})}var o=(0,h.k4)(0,e.size);return $($({},e),{},{size:o(l)})});return l>0&&(t.current=o),n.createElement(i.m,null,n.createElement(Z,{points:o,allOtherScatterProps:r,showLabels:!d}))})}function q(e){var{points:t,isAnimationActive:r}=e,a=(0,n.useRef)(null),i=a.current;return r&&t&&t.length&&(!i||i!==t)?n.createElement(Q,{props:e,previousPointsRef:a}):n.createElement(Z,{points:t,allOtherScatterProps:e,showLabels:!0})}function G(e){var{dataKey:t,points:r,stroke:n,strokeWidth:a,fill:i,name:l,hide:o,tooltipType:s}=e;return{dataDefinedOnItem:null==r?void 0:r.map(e=>e.tooltipPayload),positions:null==r?void 0:r.map(e=>e.tooltipPosition),settings:{stroke:n,strokeWidth:a,fill:i,nameKey:void 0,dataKey:t,name:(0,b.hn)(l,t),hide:o,type:s,color:i,unit:""}}}function X(e){var{displayedData:t,xAxis:r,yAxis:n,zAxis:a,scatterSettings:i,xAxisTicks:l,yAxisTicks:o,cells:s}=e,c=(0,h.Rw)(r.dataKey)?i.dataKey:r.dataKey,u=(0,h.Rw)(n.dataKey)?i.dataKey:n.dataKey,p=a&&a.dataKey,d=a?a.range:y.defaultProps.range,f=d&&d[0],m=r.scale.bandwidth?r.scale.bandwidth():0,v=n.scale.bandwidth?n.scale.bandwidth():0;return t.map((e,t)=>{var d=(0,b.F$)(e,c),y=(0,b.F$)(e,u),g=!(0,h.Rw)(p)&&(0,b.F$)(e,p)||"-",x=[{name:(0,h.Rw)(r.dataKey)?i.name:r.name||r.dataKey,unit:r.unit||"",value:d,payload:e,dataKey:c,type:i.tooltipType},{name:(0,h.Rw)(n.dataKey)?i.name:n.name||n.dataKey,unit:n.unit||"",value:y,payload:e,dataKey:u,type:i.tooltipType}];"-"!==g&&x.push({name:a.name||a.dataKey,unit:a.unit||"",value:g,payload:e,dataKey:p,type:i.tooltipType});var P=(0,b.Hv)({axis:r,ticks:l,bandSize:m,entry:e,index:t,dataKey:c}),E=(0,b.Hv)({axis:n,ticks:o,bandSize:v,entry:e,index:t,dataKey:u}),O="-"!==g?a.scale(g):f,A=Math.sqrt(Math.max(O,0)/Math.PI);return $($({},e),{},{cx:P,cy:E,x:P-A,y:E-A,width:2*A,height:2*A,size:O,node:{x:d,y,z:g},tooltipPayload:x,tooltipPosition:{x:P,y:E},payload:e},s&&s[t]&&s[t].props)})}var ee=(e,t,r)=>({x:e.cx,y:e.cy,value:"x"===r?+e.node.x:+e.node.y,errorVal:(0,b.F$)(e,t)});function et(e){var t=(0,n.useRef)((0,h.EL)("recharts-scatter-")),{hide:r,points:l,className:o,needClip:s,xAxisId:c,yAxisId:u,id:p,children:d}=e;if(r)return null;var f=(0,a.W)("recharts-scatter",o),y=(0,h.Rw)(p)?t.current:p;return n.createElement(i.m,{className:f,clipPath:s?"url(#clipPath-".concat(y,")"):null},s&&n.createElement("defs",null,n.createElement(I.W,{clipPathId:y,xAxisId:c,yAxisId:u})),n.createElement(k.zU,{xAxisId:c,yAxisId:u,data:l,dataPointFormatter:ee,errorBarOffset:0},d),n.createElement(i.m,{key:"recharts-scatter-symbols"},n.createElement(q,e)))}var er={xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!s.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"};function en(e){var t=(0,M.j)(e,er),{animationBegin:r,animationDuration:a,animationEasing:i,hide:l,isAnimationActive:s,legendType:c,lineJointType:p,lineType:d,shape:f,xAxisId:y,yAxisId:m,zAxisId:h}=t,b=V(t,W),{needClip:g}=(0,I.N)(y,m),x=(0,n.useMemo)(()=>(0,o.NN)(e.children,v.b),[e.children]),P=(0,n.useMemo)(()=>({name:e.name,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey}),[e.data,e.dataKey,e.name,e.tooltipType]),E=(0,C.W)(),O=(0,u.C)(e=>N(e,y,m,h,P,x,E));return null==g?null:n.createElement(n.Fragment,null,n.createElement(j.k,{fn:G,args:$($({},e),{},{points:O})}),n.createElement(et,H({},b,{xAxisId:y,yAxisId:m,zAxisId:h,lineType:d,lineJointType:p,legendType:c,shape:f,hide:l,isAnimationActive:s,animationBegin:r,animationDuration:a,animationEasing:i,points:O,needClip:g})))}class ea extends n.Component{render(){return n.createElement(k.Ph,{type:"scatter",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:this.props.zAxisId,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},n.createElement(z.L,{legendPayload:J(this.props)}),n.createElement(en,this.props))}}U(ea,"displayName","Scatter"),U(ea,"defaultProps",er)},16488:function(e,t,r){r.d(t,{c:function(){return o}});var n=r(2265),a=r(31057),i=r(43841),l=["axis"],o=(0,n.forwardRef)((e,t)=>n.createElement(i.R,{chartName:"ComposedChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:a.NL,categoricalChartProps:e,ref:t}))},89388:function(e,t,r){r.d(t,{B:function(){return c}});var n=r(2265),a=r(31057),i=r(40130),l=r(88077),o=["axis","item"],s={layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},c=(0,n.forwardRef)((e,t)=>{var r=(0,i.j)(e,s);return n.createElement(l.h,{chartName:"RadialBarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:o,tooltipPayloadSearcher:a.NL,categoricalChartProps:r,ref:t})})},13446:function(e,t,r){r.d(t,{G:function(){return o}});var n=r(2265),a=r(31057),i=r(43841),l=["item"],o=(0,n.forwardRef)((e,t)=>n.createElement(i.R,{chartName:"ScatterChart",defaultTooltipEventType:"item",validateTooltipEventTypes:l,tooltipPayloadSearcher:a.NL,categoricalChartProps:e,ref:t}))},34321:function(e,t,r){r.d(t,{G:function(){return eI},W:function(){return ek}});var n=r(2265),a=r(61994),i=r(11638);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,a;n=t,a=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){return"string"==typeof e?parseInt(e,10):e}function u(e,t){var r=Number("".concat(t.cx||e.cx)),n=Number("".concat(t.cy||e.cy));return s(s(s({},t),e),{},{cx:r,cy:n})}function p(e){return n.createElement(i.b,l({shapeType:"sector",propTransformer:u},e))}var d=r(9841),f=r(82944),y=r(34067),m=r(58772),v=r(20407),h=r(16630),b=r(49037),g=r(41637),x=r(44296),P=r(35623),E=r(35450),O=r(13790),A=e=>n.createElement(O.E,e),w=r(92713),j=r(22932),k=r(98628),I=r(80796),S=r(35953),K=r(11251),T=r(40304),N=(e,t,r)=>{switch(t){case"angleAxis":return(0,I.dc)(e,r);case"radiusAxis":return(0,I.Au)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},C=(e,t,r)=>{switch(t){case"angleAxis":return(0,I.YM)(e,r);case"radiusAxis":return(0,I.n2)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},D=(0,w.P1)([N,k.cV,K.cN,C],k.Jw),z=(0,w.P1)([S.rE,K.PQ,k.ub,T.z],k.UC),R=(0,w.P1)([S.rE,N,k.cV,D,K.X4,C,k._P,z,T.z],k.kh),L=(0,w.P1)([S.rE,N,D,C,k._P,z,T.z],k.pg),M=r(9145),B=r(33968);function F(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function W(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?F(Object(r),!0).forEach(function(t){var n,a;n=t,a=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V=(0,w.P1)([(e,t)=>(0,I.Au)(e,t),(e,t)=>D(e,"radiusAxis",t)],(e,t)=>{if(null!=e&&null!=t)return W(W({},e),{},{scale:t})}),H=(e,t,r,n)=>L(e,"radiusAxis",t,n),Y=(0,w.P1)([(e,t,r)=>(0,I.dc)(e,r),(e,t,r)=>D(e,"angleAxis",r)],(e,t)=>{if(null!=e&&null!=t)return W(W({},e),{},{scale:t})}),$=(e,t,r,n)=>R(e,"angleAxis",r,n),U=(0,w.P1)([K.vE,(e,t,r,n)=>n],(e,t)=>{if(e.some(e=>"radialBar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId))return t}),J=(0,w.P1)([S.rE,V,H,Y,$],(e,t,r,n,a)=>(0,b.NA)(e,"radiusAxis")?(0,b.zT)(t,r,!1):(0,b.zT)(n,a,!1)),_=(0,w.P1)([Y,V,S.rE],(e,t,r)=>{var n="radial"===r?e:t;if(null!=n&&null!=n.scale)return(0,b.Yj)({numericAxis:n})}),Z=(e,t,r,n,a)=>n.maxBarSize,Q=(0,w.P1)([S.rE,K.vE,(e,t,r,n,a)=>r,(e,t,r,n,a)=>t],(e,t,r,n)=>t.filter(t=>"centric"===e?t.angleAxisId===r:t.radiusAxisId===n).filter(e=>!1===e.hide).filter(e=>"radialBar"===e.type)),q=(0,w.P1)([Q,B.X8,()=>void 0],M.fu),G=(0,w.P1)([S.rE,B.qy,Y,$,V,H,Z],(e,t,r,n,a,i,l)=>{var o,s,c,u,p=(0,h.Rw)(l)?t:l;return"centric"===e?null!==(c=null!==(u=(0,b.zT)(r,n,!0))&&void 0!==u?u:p)&&void 0!==c?c:0:null!==(o=null!==(s=(0,b.zT)(a,i,!0))&&void 0!==s?s:p)&&void 0!==o?o:0}),X=(0,w.P1)([q,B.qy,B.wK,B.sd,G,J,Z],M.Ay),ee=(0,w.P1)([X,U],(e,t)=>{if(null!=e&&null!=t){var r=e.find(e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),et=(0,w.P1)([K.g6,K.Hy,B.Qw],k.CK),er=(0,w.P1)([(e,t,r)=>"centric"===(0,S.rE)(e)?et(e,"radiusAxis",t):et(e,"angleAxis",r),U],M.BU),en=(0,w.P1)([Y,$,V,H,j.iP,U,J,S.rE,_,I.HW,(e,t,r,n,a)=>a,ee,er],(e,t,r,n,a,i,l,o,s,c,u,p,d)=>{var{chartData:f,dataStartIndex:y,dataEndIndex:m}=a;if(null==i||null==r||null==e||null==f||null==l||null==p||"centric"!==o&&"radial"!==o||null==n)return[];var{dataKey:v,minPointSize:h}=i,{cx:b,cy:g,startAngle:x,endAngle:P}=c,E=f.slice(y,m+1),O="centric"===o?r:e,A=d?O.scale.domain():null;return ek({angleAxis:e,angleAxisTicks:t,bandSize:l,baseValue:s,cells:u,cx:b,cy:g,dataKey:v,dataStartIndex:y,displayedData:E,endAngle:P,layout:o,minPointSize:h,pos:p,radiusAxis:r,radiusAxisTicks:n,stackedData:d,stackedDomain:A,startAngle:x})}),ea=(0,w.P1)([j.RV,(e,t)=>t],(e,t)=>{var{chartData:r,dataStartIndex:n,dataEndIndex:a}=e;if(null==r)return[];var i=r.slice(n,a+1);return 0===i.length?[]:i.map(e=>({type:t,value:e.name,color:e.fill,payload:e}))}),ei=r(39040),el=r(31944),eo=r(62658),es=r(59087),ec=r(46595),eu=["shape","activeShape","cornerRadius"],ep=["onMouseEnter","onClick","onMouseLeave"],ed=["value","background"];function ef(){return(ef=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ey(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function em(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ey(Object(r),!0).forEach(function(t){ev(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ey(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ev(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eh(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var eb=[];function eg(e){var{sectors:t,allOtherRadialBarProps:r,showLabels:a}=e,{shape:i,activeShape:l,cornerRadius:o}=r,s=eh(r,eu),u=(0,f.L6)(s,!1),d=(0,ei.C)(el.Ve),{onMouseEnter:y,onClick:v,onMouseLeave:h}=r,b=eh(r,ep),P=(0,x.Df)(y,r.dataKey),E=(0,x.oQ)(h),O=(0,x.nC)(v,r.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,t)=>{var r=l&&d===String(t),a=P(e,t),f=E(e,t),y=O(e,t),m=em(em(em(em({},u),{},{cornerRadius:c(o)},e),(0,g.bw)(b,e,t)),{},{onMouseEnter:a,onMouseLeave:f,onClick:y,key:"sector-".concat(t),className:"recharts-radial-bar-sector ".concat(e.className),forceCornerRadius:s.forceCornerRadius,cornerIsExternal:s.cornerIsExternal,isActive:r,option:r?l:i});return n.createElement(p,m)}),a&&m.e.renderCallByParent(r,t))}function ex(e){var{props:t,previousSectorsRef:r}=e,{data:a,isAnimationActive:i,animationBegin:l,animationDuration:o,animationEasing:s,onAnimationEnd:c,onAnimationStart:u}=t,p=(0,es.i)(t,"recharts-radialbar-"),f=r.current,[y,m]=(0,n.useState)(!0),v=(0,n.useCallback)(()=>{"function"==typeof c&&c(),m(!1)},[c]),b=(0,n.useCallback)(()=>{"function"==typeof u&&u(),m(!0)},[u]);return n.createElement(ec.r,{begin:l,duration:o,isActive:i,easing:s,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:v,key:p},e=>{var{t:i}=e,l=1===i?a:(null!=a?a:eb).map((e,t)=>{var r=f&&f[t];if(r){var n=(0,h.k4)(r.startAngle,e.startAngle),a=(0,h.k4)(r.endAngle,e.endAngle);return em(em({},e),{},{startAngle:n(i),endAngle:a(i)})}var{endAngle:l,startAngle:o}=e,s=(0,h.k4)(o,l);return em(em({},e),{},{endAngle:s(i)})});return i>0&&(r.current=null!=l?l:null),n.createElement(d.m,null,n.createElement(eg,{sectors:null!=l?l:eb,allOtherRadialBarProps:t,showLabels:!y}))})}function eP(e){var{data:t=[],isAnimationActive:r}=e,a=(0,n.useRef)(null),i=a.current;return r&&t&&t.length&&(!i||i!==t)?n.createElement(ex,{props:e,previousSectorsRef:a}):n.createElement(eg,{sectors:t,allOtherRadialBarProps:e,showLabels:!0})}function eE(e){var t=(0,ei.C)(t=>ea(t,e.legendType));return n.createElement(eo.t,{legendPayload:null!=t?t:[]})}function eO(e){var{dataKey:t,data:r,stroke:n,strokeWidth:a,name:i,hide:l,fill:o,tooltipType:s}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:a,fill:o,nameKey:void 0,dataKey:t,name:(0,b.hn)(i,t),hide:l,type:s,color:o,unit:""}}}class eA extends n.PureComponent{renderBackground(e){if(null==e)return null;var{cornerRadius:t}=this.props,r=(0,f.L6)(this.props.background,!1);return e.map((e,i)=>{var{value:l,background:o}=e,s=eh(e,ed);if(!o)return null;var u=em(em(em(em(em({cornerRadius:c(t)},s),{},{fill:"#eee"},o),r),(0,g.bw)(this.props,e,i)),{},{index:i,key:"sector-".concat(i),className:(0,a.W)("recharts-radial-bar-background-sector",null==r?void 0:r.className),option:o,isActive:!1});return n.createElement(p,u)})}render(){var{hide:e,data:t,className:r,background:i}=this.props;if(e)return null;var l=(0,a.W)("recharts-area",r);return n.createElement(d.m,{className:l},i&&n.createElement(d.m,{className:"recharts-radial-bar-background"},this.renderBackground(t)),n.createElement(d.m,{className:"recharts-radial-bar-sectors"},n.createElement(eP,this.props)))}}function ew(e){var t,r=(0,f.NN)(e.children,v.b),a={dataKey:e.dataKey,minPointSize:e.minPointSize,stackId:e.stackId,maxBarSize:e.maxBarSize,barSize:e.barSize},i=null!==(t=(0,ei.C)(t=>en(t,e.radiusAxisId,e.angleAxisId,a,r)))&&void 0!==t?t:eb;return n.createElement(n.Fragment,null,n.createElement(P.k,{fn:eO,args:em(em({},e),{},{data:i})}),n.createElement(eA,ef({},e,{data:i})))}var ej={angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!y.x.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1};function ek(e){var{displayedData:t,stackedData:r,dataStartIndex:n,stackedDomain:a,dataKey:i,baseValue:l,layout:o,radiusAxis:s,radiusAxisTicks:c,bandSize:u,pos:p,angleAxis:d,minPointSize:f,cx:y,cy:m,angleAxisTicks:v,cells:g,startAngle:x,endAngle:P}=e;return(null!=t?t:[]).map((e,t)=>{var E,O,A,w,j,k;if(r?E=(0,b.Vv)(r[n+t],a):Array.isArray(E=(0,b.F$)(e,i))||(E=[l,E]),"radial"===o){O=(0,b.Fy)({axis:s,ticks:c,bandSize:u,offset:p.offset,entry:e,index:t}),j=d.scale(E[1]),w=d.scale(E[0]),A=(null!=O?O:0)+p.size;var I=j-w;Math.abs(f)>0&&Math.abs(I)<Math.abs(f)&&(j+=(0,h.uY)(I||f)*(Math.abs(f)-Math.abs(I))),k={background:{cx:y,cy:m,innerRadius:O,outerRadius:A,startAngle:x,endAngle:P}}}else{O=s.scale(E[0]),A=s.scale(E[1]),j=(null!=(w=(0,b.Fy)({axis:d,ticks:v,bandSize:u,offset:p.offset,entry:e,index:t}))?w:0)+p.size;var S=A-O;Math.abs(f)>0&&Math.abs(S)<Math.abs(f)&&(A+=(0,h.uY)(S||f)*(Math.abs(f)-Math.abs(S)))}return em(em(em({},e),k),{},{payload:e,value:r?E:E[1],cx:y,cy:m,innerRadius:O,outerRadius:A,startAngle:w,endAngle:j},g&&g[t]&&g[t].props)})}class eI extends n.PureComponent{render(){var e,t,r;return n.createElement(n.Fragment,null,n.createElement(E.Y,null),n.createElement(A,{data:void 0,dataKey:this.props.dataKey,hide:null!==(e=this.props.hide)&&void 0!==e?e:ej.hide,angleAxisId:null!==(t=this.props.angleAxisId)&&void 0!==t?t:ej.angleAxisId,radiusAxisId:null!==(r=this.props.radiusAxisId)&&void 0!==r?r:ej.radiusAxisId,stackId:this.props.stackId,barSize:this.props.barSize,type:"radialBar"}),n.createElement(eE,this.props),n.createElement(ew,this.props))}}ev(eI,"displayName","RadialBar"),ev(eI,"defaultProps",ej)}}]);