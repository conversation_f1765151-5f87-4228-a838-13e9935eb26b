"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4057,2149],{2014:function(e,t,a){a.d(t,{default:function(){return i}});var n=a(57437),r=a(2265),s=a(99376),o=a(63119);function i(e){let{children:t,allowedRoles:a}=e,[i,c]=(0,r.useState)(null),[d,u]=(0,r.useState)(!0),l=(0,s.useRouter)();return((0,r.useEffect)(()=>{let e=localStorage.getItem("token"),t=localStorage.getItem("user");if(!e||!t){l.push("/auth/login");return}let n=JSON.parse(t);if(!a.includes(n.role)){switch(n.role){case"admin":case"super_admin":l.push("/admin/dashboard");break;case"company":l.push("/company/dashboard");break;case"individual":l.push("/user/dashboard");break;case"government":l.push("/government/dashboard");break;default:l.push("/auth/login")}return}if("admin"!==n.role&&"super_admin"!==n.role&&"approved"!==n.status){l.push("/account-status");return}c(n),u(!1)},[l,a]),d)?(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,n.jsx)("p",{className:"mt-4 text-muted-foreground",children:"جاري التحميل..."})]})}):i?(0,n.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,n.jsx)(o.Z,{userRole:i.role}),(0,n.jsx)("main",{className:"flex-1 overflow-auto",children:(0,n.jsx)("div",{className:"p-6",children:t})})]}):null}},35974:function(e,t,a){a.d(t,{C:function(){return i}});var n=a(57437);a(2265);var r=a(90535),s=a(94508);let o=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...r}=e;return(0,n.jsx)("div",{className:(0,s.cn)(o({variant:a}),t),...r})}},66070:function(e,t,a){a.d(t,{Ol:function(){return i},SZ:function(){return d},Zb:function(){return o},aY:function(){return u},ll:function(){return c}});var n=a(57437),r=a(2265),s=a(94508);let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});o.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",a),...r})});i.displayName="CardHeader";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",a),...r})});d.displayName="CardDescription";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",a),...r})});u.displayName="CardContent",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"},24895:function(e,t,a){a.d(t,{p:function(){return i}});var n=a(2265);let r={toasts:[]},s=[];function o(e){r=e,s.forEach(t=>t(e))}function i(){let[e,t]=(0,n.useState)(r);return(0,n.useEffect)(()=>(s.push(t),()=>{s=s.filter(e=>e!==t)}),[]),{toast:e=>{let{...t}=e;console.log("Toast called with:",t);let a=Math.random().toString(36).substr(2,9),n={...t,id:a},s={...r,toasts:[...r.toasts,n]};return console.log("Updating toast state with:",s),o(s),setTimeout(()=>{o({...r,toasts:r.toasts.filter(e=>e.id!==a)})},8e3),{id:a,dismiss:()=>{o({...r,toasts:r.toasts.filter(e=>e.id!==a)})}}},toasts:e.toasts}}},29116:function(e,t,a){a.d(t,{Sb:function(){return i},Xy:function(){return o},kv:function(){return r},zg:function(){return s}});let n=a(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let r={register:e=>n.post("/auth/register",e),login:e=>n.post("/auth/login",e),logout:()=>n.post("/auth/logout"),verifyEmail:e=>n.post("/auth/verify-email",{token:e}),resendVerification:e=>n.post("/auth/resend-verification",{email:e}),forgotPassword:e=>n.post("/auth/forgot-password",{email:e}),resetPassword:e=>n.post("/auth/reset-password",e)},s={getAll:()=>n.get("/auctions"),getById:e=>n.get("/auctions/".concat(e)),create:e=>n.post("/auctions",e),update:(e,t)=>n.put("/auctions/".concat(e),t),delete:e=>n.delete("/auctions/".concat(e)),placeBid:(e,t)=>n.post("/auctions/".concat(e,"/bid"),{amount:t})},o={getFavorites:e=>n.get("/favorites",{params:e}),addFavorite:e=>n.post("/favorites",e),removeFavorite:(e,t)=>n.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,a)=>n.put("/favorites/".concat(e,"/").concat(t),a),checkFavorite:(e,t)=>n.get("/favorites/check/".concat(e,"/").concat(t))},i={getDashboardStats:()=>n.get("/admin/dashboard"),getPendingAccounts:()=>n.get("/admin/pending-accounts"),approvePendingAccount:e=>n.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>n.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>n.get("/admin/users",{params:e}),getById:e=>n.get("/admin/users/".concat(e)),update:(e,t)=>n.put("/admin/users/".concat(e),t),delete:e=>n.delete("/admin/users/".concat(e)),activate:e=>n.post("/admin/users/".concat(e,"/activate")),deactivate:e=>n.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>n.get("/admin/auctions",{params:e}),getById:e=>n.get("/admin/auctions/".concat(e)),approve:e=>n.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>n.get("/admin/tenders",{params:e}),getById:e=>n.get("/admin/tenders/".concat(e)),approve:e=>n.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/tenders/".concat(e))},getTender:e=>n.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>n.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>n.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,a)=>n.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),a),reports:{getFinancialReport:e=>n.get("/admin/reports/financial",{params:e}),getUserReport:e=>n.get("/admin/reports/users",{params:e}),getActivityReport:e=>n.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>n.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>n.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>n.get("/admin/settings"),update:e=>n.put("/admin/settings",e),backup:()=>n.post("/admin/settings/backup"),restore:e=>n.post("/admin/settings/restore/".concat(e))}};t.ZP=n}}]);