"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4863],{30401:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},40875:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},22135:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},57864:function(e,t,n){let r;n.d(t,{VY:function(){return rv},ZA:function(){return rm},JO:function(){return rf},ck:function(){return ry},wU:function(){return rb},eT:function(){return rw},__:function(){return rg},h_:function(){return rp},fC:function(){return rc},$G:function(){return rE},u_:function(){return rx},Z0:function(){return rS},xz:function(){return rs},B4:function(){return rd},l_:function(){return rh}});var o,i,l,a,u,c,s,d,f=n(2265),p=n(54887);function v(e,[t,n]){return Math.min(n,Math.max(t,e))}var h=n(6741),m=n(58068),g=n(98575),y=n(73966),w=n(29114),b=n(66840),x=n(26606),E=n(57437),S="dismissableLayer.update",C=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),R=f.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:a,onInteractOutside:u,onDismiss:c,...d}=e,p=f.useContext(C),[v,m]=f.useState(null),y=null!==(r=null==v?void 0:v.ownerDocument)&&void 0!==r?r:null===(n=globalThis)||void 0===n?void 0:n.document,[,w]=f.useState({}),R=(0,g.e)(t,e=>m(e)),L=Array.from(p.layers),[k]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),P=L.indexOf(k),M=v?L.indexOf(v):-1,D=p.layersWithOutsidePointerEventsDisabled.size>0,N=M>=P,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,x.W)(e),o=f.useRef(!1),i=f.useRef(()=>{});return f.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){A("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...p.branches].some(e=>e.contains(t));!N||n||(null==l||l(e),null==u||u(e),e.defaultPrevented||null==c||c())},y),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,x.W)(e),o=f.useRef(!1);return f.useEffect(()=>{let e=e=>{e.target&&!o.current&&A("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...p.branches].some(e=>e.contains(t))||(null==a||a(e),null==u||u(e),e.defaultPrevented||null==c||c())},y);return!function(e,t=globalThis?.document){let n=(0,x.W)(e);f.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M!==p.layers.size-1||(null==i||i(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},y),f.useEffect(()=>{if(v)return o&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(s=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(v)),p.layers.add(v),T(),()=>{o&&1===p.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=s)}},[v,y,o,p]),f.useEffect(()=>()=>{v&&(p.layers.delete(v),p.layersWithOutsidePointerEventsDisabled.delete(v),T())},[v,p]),f.useEffect(()=>{let e=()=>w({});return document.addEventListener(S,e),()=>document.removeEventListener(S,e)},[]),(0,E.jsx)(b.WV.div,{...d,ref:R,style:{pointerEvents:D?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,h.M)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,h.M)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,h.M)(e.onPointerDownCapture,j.onPointerDownCapture)})});function T(){let e=new CustomEvent(S);document.dispatchEvent(e)}function A(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,b.jH)(i,l):i.dispatchEvent(l)}R.displayName="DismissableLayer",f.forwardRef((e,t)=>{let n=f.useContext(C),r=f.useRef(null),o=(0,g.e)(t,r);return f.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,E.jsx)(b.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var L=0;function k(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var P="focusScope.autoFocusOnMount",M="focusScope.autoFocusOnUnmount",D={bubbles:!1,cancelable:!0},N=f.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[a,u]=f.useState(null),c=(0,x.W)(o),s=(0,x.W)(i),d=f.useRef(null),p=(0,g.e)(t,e=>u(e)),v=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let e=function(e){if(v.paused||!a)return;let t=e.target;a.contains(t)?d.current=t:W(d.current,{select:!0})},t=function(e){if(v.paused||!a)return;let t=e.relatedTarget;null===t||a.contains(t)||W(d.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&W(a)});return a&&n.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,a,v.paused]),f.useEffect(()=>{if(a){I.add(v);let e=document.activeElement;if(!a.contains(e)){let t=new CustomEvent(P,D);a.addEventListener(P,c),a.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(W(r,{select:t}),document.activeElement!==n)return}(j(a).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&W(a))}return()=>{a.removeEventListener(P,c),setTimeout(()=>{let t=new CustomEvent(M,D);a.addEventListener(M,s),a.dispatchEvent(t),t.defaultPrevented||W(null!=e?e:document.body,{select:!0}),a.removeEventListener(M,s),I.remove(v)},0)}}},[a,c,s,v]);let h=f.useCallback(e=>{if(!n&&!r||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=j(e);return[O(t,e),O(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&W(i,{select:!0})):(e.preventDefault(),n&&W(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,v.paused]);return(0,E.jsx)(b.WV.div,{tabIndex:-1,...l,ref:p,onKeyDown:h})});function j(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function O(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function W(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}N.displayName="FocusScope";var I=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=H(r,e)).unshift(e)},remove(e){var t;null===(t=(r=H(r,e))[0])||void 0===t||t.resume()}});function H(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var F=n(99255);let V=["top","right","bottom","left"],B=Math.min,_=Math.max,z=Math.round,K=Math.floor,Y=e=>({x:e,y:e}),U={left:"right",right:"left",bottom:"top",top:"bottom"},Z={start:"end",end:"start"};function X(e,t){return"function"==typeof e?e(t):e}function q(e){return e.split("-")[0]}function $(e){return e.split("-")[1]}function G(e){return"x"===e?"y":"x"}function J(e){return"y"===e?"height":"width"}let Q=new Set(["top","bottom"]);function ee(e){return Q.has(q(e))?"y":"x"}function et(e){return e.replace(/start|end/g,e=>Z[e])}let en=["left","right"],er=["right","left"],eo=["top","bottom"],ei=["bottom","top"];function el(e){return e.replace(/left|right|bottom|top/g,e=>U[e])}function ea(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function eu(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ec(e,t,n){let r,{reference:o,floating:i}=e,l=ee(t),a=G(ee(t)),u=J(a),c=q(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch($(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let es=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=ec(c,r,u),f=r,p={},v=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:m,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&v<=50&&(v++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=ec(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function ed(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=X(t,e),v=ea(p),h=a[f?"floating"===d?"reference":"floating":d],m=eu(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=eu(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-b.top+v.top)/w.y,bottom:(b.bottom-m.bottom+v.bottom)/w.y,left:(m.left-b.left+v.left)/w.x,right:(b.right-m.right+v.right)/w.x}}function ef(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ep(e){return V.some(t=>e[t]>=0)}let ev=new Set(["left","top"]);async function eh(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=q(n),a=$(n),u="y"===ee(n),c=ev.has(l)?-1:1,s=i&&u?-1:1,d=X(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof v&&(p="end"===a?-1*v:v),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function em(){return"undefined"!=typeof window}function eg(e){return eb(e)?(e.nodeName||"").toLowerCase():"#document"}function ey(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ew(e){var t;return null==(t=(eb(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eb(e){return!!em()&&(e instanceof Node||e instanceof ey(e).Node)}function ex(e){return!!em()&&(e instanceof Element||e instanceof ey(e).Element)}function eE(e){return!!em()&&(e instanceof HTMLElement||e instanceof ey(e).HTMLElement)}function eS(e){return!!em()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ey(e).ShadowRoot)}let eC=new Set(["inline","contents"]);function eR(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eW(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!eC.has(o)}let eT=new Set(["table","td","th"]),eA=[":popover-open",":modal"];function eL(e){return eA.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ek=["transform","translate","scale","rotate","perspective"],eP=["transform","translate","scale","rotate","perspective","filter"],eM=["paint","layout","strict","content"];function eD(e){let t=eN(),n=ex(e)?eW(e):e;return ek.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eP.some(e=>(n.willChange||"").includes(e))||eM.some(e=>(n.contain||"").includes(e))}function eN(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ej=new Set(["html","body","#document"]);function eO(e){return ej.has(eg(e))}function eW(e){return ey(e).getComputedStyle(e)}function eI(e){return ex(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eH(e){if("html"===eg(e))return e;let t=e.assignedSlot||e.parentNode||eS(e)&&e.host||ew(e);return eS(t)?t.host:t}function eF(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eH(t);return eO(n)?t.ownerDocument?t.ownerDocument.body:t.body:eE(n)&&eR(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=ey(o);if(i){let e=eV(l);return t.concat(l,l.visualViewport||[],eR(o)?o:[],e&&n?eF(e):[])}return t.concat(o,eF(o,[],n))}function eV(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eB(e){let t=eW(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eE(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=z(n)!==i||z(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function e_(e){return ex(e)?e:e.contextElement}function ez(e){let t=e_(e);if(!eE(t))return Y(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eB(t),l=(i?z(n.width):n.width)/r,a=(i?z(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eK=Y(0);function eY(e){let t=ey(e);return eN()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eK}function eU(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=e_(e),a=Y(1);t&&(r?ex(r)&&(a=ez(r)):a=ez(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ey(l))&&o)?eY(l):Y(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=ey(l),t=r&&ex(r)?ey(r):r,n=e,o=eV(n);for(;o&&r&&t!==n;){let e=ez(o),t=o.getBoundingClientRect(),r=eW(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eV(n=ey(o))}}return eu({width:d,height:f,x:c,y:s})}function eZ(e,t){let n=eI(e).scrollLeft;return t?t.left+n:eU(ew(e)).left+n}function eX(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eZ(e,r)),y:r.top+t.scrollTop}}let eq=new Set(["absolute","fixed"]);function e$(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ey(e),r=ew(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eN();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=ew(e),n=eI(e),r=e.ownerDocument.body,o=_(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=_(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eZ(e),a=-n.scrollTop;return"rtl"===eW(r).direction&&(l+=_(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(ew(e));else if(ex(t))r=function(e,t){let n=eU(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eE(e)?ez(e):Y(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eY(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return eu(r)}function eG(e){return"static"===eW(e).position}function eJ(e,t){if(!eE(e)||"fixed"===eW(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ew(e)===n&&(n=n.ownerDocument.body),n}function eQ(e,t){var n;let r=ey(e);if(eL(e))return r;if(!eE(e)){let t=eH(e);for(;t&&!eO(t);){if(ex(t)&&!eG(t))return t;t=eH(t)}return r}let o=eJ(e,t);for(;o&&(n=o,eT.has(eg(n)))&&eG(o);)o=eJ(o,t);return o&&eO(o)&&eG(o)&&!eD(o)?r:o||function(e){let t=eH(e);for(;eE(t)&&!eO(t);){if(eD(t))return t;if(eL(t))break;t=eH(t)}return null}(e)||r}let e0=async function(e){let t=this.getOffsetParent||eQ,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eE(t),o=ew(t),i="fixed"===n,l=eU(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=Y(0);if(r||!r&&!i){if(("body"!==eg(t)||eR(o))&&(a=eI(t)),r){let e=eU(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eZ(o))}i&&!r&&o&&(u.x=eZ(o));let c=!o||r||i?Y(0):eX(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},e1={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=ew(r),a=!!t&&eL(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=Y(1),s=Y(0),d=eE(r);if((d||!d&&!i)&&(("body"!==eg(r)||eR(l))&&(u=eI(r)),eE(r))){let e=eU(r);c=ez(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?Y(0):eX(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:ew,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eL(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eF(e,[],!1).filter(e=>ex(e)&&"body"!==eg(e)),o=null,i="fixed"===eW(e).position,l=i?eH(e):e;for(;ex(l)&&!eO(l);){let t=eW(l),n=eD(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eq.has(o.position)||eR(l)&&!n&&function e(t,n){let r=eH(t);return!(r===n||!ex(r)||eO(r))&&("fixed"===eW(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eH(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=e$(t,n,o);return e.top=_(r.top,e.top),e.right=B(r.right,e.right),e.bottom=B(r.bottom,e.bottom),e.left=_(r.left,e.left),e},e$(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eQ,getElementRects:e0,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eB(e);return{width:t,height:n}},getScale:ez,isElement:ex,isRTL:function(e){return"rtl"===eW(e).direction}};function e2(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e6=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=X(e,t)||{};if(null==c)return{};let d=ea(s),f={x:n,y:r},p=G(ee(o)),v=J(p),h=await l.getDimensions(c),m="y"===p,g=m?"clientHeight":"clientWidth",y=i.reference[v]+i.reference[p]-f[p]-i.floating[v],w=f[p]-i.reference[p],b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),x=b?b[g]:0;x&&await (null==l.isElement?void 0:l.isElement(b))||(x=a.floating[g]||i.floating[v]);let E=x/2-h[v]/2-1,S=B(d[m?"top":"left"],E),C=B(d[m?"bottom":"right"],E),R=x-h[v]-C,T=x/2-h[v]/2+(y/2-w/2),A=_(S,B(T,R)),L=!u.arrow&&null!=$(o)&&T!==A&&i.reference[v]/2-(T<S?S:C)-h[v]/2<0,k=L?T<S?T-S:T-R:0;return{[p]:f[p]+k,data:{[p]:A,centerOffset:T-A-k,...L&&{alignmentOffset:k}},reset:L}}}),e5=(e,t,n)=>{let r=new Map,o={platform:e1,...n},i={...o.platform,_c:r};return es(e,t,{...o,platform:i})};var e8="undefined"!=typeof document?f.useLayoutEffect:function(){};function e3(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e3(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e3(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e7(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e9(e,t){let n=e7(e);return Math.round(t*n)/n}function e4(e){let t=f.useRef(e);return e8(()=>{t.current=e}),t}let te=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e6({element:n.current,padding:r}).fn(t):{}:n?e6({element:n,padding:r}).fn(t):{}}}),tt=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:l,middlewareData:a}=e,u=await eh(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}),options:[e,t]}},tn=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=X(n,e),c={x:t,y:r},s=await ed(e,u),d=ee(q(o)),f=G(d),p=c[f],v=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=_(n,B(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=v+s[e],r=v-s[t];v=_(n,B(v,r))}let h=a.fn({...e,[f]:p,[d]:v});return{...h,data:{x:h.x-t,y:h.y-r,enabled:{[f]:i,[d]:l}}}}}),options:[e,t]}},tr=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:l}=e,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=X(n,e),s={x:t,y:r},d=ee(o),f=G(d),p=s[f],v=s[d],h=X(a,e),m="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=ev.has(q(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[f]:p,[d]:v}}}),options:[e,t]}},to=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=e,{mainAxis:p=!0,crossAxis:v=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=X(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let b=q(a),x=ee(s),E=q(s)===s,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=h||(E||!y?[el(s)]:function(e){let t=el(e);return[et(e),t,et(t)]}(s)),R="none"!==g;!h&&R&&C.push(...function(e,t,n,r){let o=$(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?er:en;return t?en:er;case"left":case"right":return t?eo:ei;default:return[]}}(q(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(et)))),i}(s,y,g,S));let T=[s,...C],A=await ed(e,w),L=[],k=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&L.push(A[b]),v){let e=function(e,t,n){void 0===n&&(n=!1);let r=$(e),o=G(ee(e)),i=J(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=el(l)),[l,el(l)]}(a,c,S);L.push(A[e[0]],A[e[1]])}if(k=[...k,{placement:a,overflows:L}],!L.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=T[e];if(t&&(!("alignment"===v&&x!==ee(t))||k.every(e=>e.overflows[0]>0&&ee(e.placement)===x)))return{data:{index:e,overflows:k},reset:{placement:t}};let n=null==(i=k.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=k.filter(e=>{if(R){let t=ee(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},ti=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=e,{apply:s=()=>{},...d}=X(n,e),f=await ed(e,d),p=q(l),v=$(l),h="y"===ee(l),{width:m,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===v?"top":"bottom");let y=g-f.top-f.bottom,w=m-f.left-f.right,b=B(g-f[o],y),x=B(m-f[i],w),E=!e.middlewareData.shift,S=b,C=x;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(C=w),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!v){let e=_(f.left,0),t=_(f.right,0),n=_(f.top,0),r=_(f.bottom,0);h?C=m-2*(0!==e||0!==t?e+t:_(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:_(f.top,f.bottom))}await s({...e,availableWidth:C,availableHeight:S});let R=await u.getDimensions(c.floating);return m!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},tl=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=X(n,e);switch(r){case"referenceHidden":{let n=ef(await ed(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:ep(n)}}}case"escaped":{let n=ef(await ed(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:ep(n)}}}default:return{}}}}),options:[e,t]}},ta=(e,t)=>({...te(e),options:[e,t]});var tu=f.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,E.jsx)(b.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,E.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tu.displayName="Arrow";var tc=n(61188),ts=n(90420),td="Popper",[tf,tp]=(0,y.b)(td),[tv,th]=tf(td),tm=e=>{let{__scopePopper:t,children:n}=e,[r,o]=f.useState(null);return(0,E.jsx)(tv,{scope:t,anchor:r,onAnchorChange:o,children:n})};tm.displayName=td;var tg="PopperAnchor",ty=f.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=th(tg,n),l=f.useRef(null),a=(0,g.e)(t,l);return f.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||l.current)}),r?null:(0,E.jsx)(b.WV.div,{...o,ref:a})});ty.displayName=tg;var tw="PopperContent",[tb,tx]=tf(tw),tE=f.forwardRef((e,t)=>{var n,r,o,i,l,a,u,c;let{__scopePopper:s,side:d="bottom",sideOffset:v=0,align:h="center",alignOffset:m=0,arrowPadding:y=0,avoidCollisions:w=!0,collisionBoundary:S=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:T=!1,updatePositionStrategy:A="optimized",onPlaced:L,...k}=e,P=th(tw,s),[M,D]=f.useState(null),N=(0,g.e)(t,e=>D(e)),[j,O]=f.useState(null),W=(0,ts.t)(j),I=null!==(u=null==W?void 0:W.width)&&void 0!==u?u:0,H=null!==(c=null==W?void 0:W.height)&&void 0!==c?c:0,F="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},V=Array.isArray(S)?S:[S],z=V.length>0,Y={padding:F,boundary:V.filter(tT),altBoundary:z},{refs:U,floatingStyles:Z,placement:X,isPositioned:q,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:a=!0,whileElementsMounted:u,open:c}=e,[s,d]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,h]=f.useState(r);e3(v,r)||h(r);let[m,g]=f.useState(null),[y,w]=f.useState(null),b=f.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=f.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||m,S=l||y,C=f.useRef(null),R=f.useRef(null),T=f.useRef(s),A=null!=u,L=e4(u),k=e4(o),P=e4(c),M=f.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:v};k.current&&(e.platform=k.current),e5(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};D.current&&!e3(T.current,t)&&(T.current=t,p.flushSync(()=>{d(t)}))})},[v,t,n,k,P]);e8(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let D=f.useRef(!1);e8(()=>(D.current=!0,()=>{D.current=!1}),[]),e8(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(L.current)return L.current(E,S,M);M()}},[E,S,M,L,A]);let N=f.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),j=f.useMemo(()=>({reference:E,floating:S}),[E,S]),O=f.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=e9(j.floating,s.x),r=e9(j.floating,s.y);return a?{...e,transform:"translate("+t+"px, "+r+"px)",...e7(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,a,j.floating,s.x,s.y]);return f.useMemo(()=>({...s,update:M,refs:N,elements:j,floatingStyles:O}),[s,M,N,j,O])}({strategy:"fixed",placement:d+("center"!==h?"-"+h:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=e_(e),d=i||l?[...s?eF(s):[],...eF(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=ew(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let v=K(d),h=K(o.clientWidth-(s+f)),m={rootMargin:-v+"px "+-h+"px "+-K(o.clientHeight-(d+p))+"px "+-K(s)+"px",threshold:_(0,B(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||e2(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,m)}r.observe(e)}(!0),i}(s,n):null,p=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),s&&!c&&v.observe(s),v.observe(t));let h=c?eU(e):null;return c&&function t(){let r=eU(e);h&&!e2(h,r)&&n(),h=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=v)||e.disconnect(),v=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:P.anchor},middleware:[tt({mainAxis:v+H,alignmentAxis:m}),w&&tn({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?tr():void 0,...Y}),w&&to({...Y}),ti({...Y,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&ta({element:j,padding:y}),tA({arrowWidth:I,arrowHeight:H}),T&&tl({strategy:"referenceHidden",...Y})]}),[G,J]=tL(X),Q=(0,x.W)(L);(0,tc.b)(()=>{q&&(null==Q||Q())},[q,Q]);let ee=null===(n=$.arrow)||void 0===n?void 0:n.x,et=null===(r=$.arrow)||void 0===r?void 0:r.y,en=(null===(o=$.arrow)||void 0===o?void 0:o.centerOffset)!==0,[er,eo]=f.useState();return(0,tc.b)(()=>{M&&eo(window.getComputedStyle(M).zIndex)},[M]),(0,E.jsx)("div",{ref:U.setFloating,"data-radix-popper-content-wrapper":"",style:{...Z,transform:q?Z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null===(i=$.transformOrigin)||void 0===i?void 0:i.x,null===(l=$.transformOrigin)||void 0===l?void 0:l.y].join(" "),...(null===(a=$.hide)||void 0===a?void 0:a.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,E.jsx)(tb,{scope:s,placedSide:G,onArrowChange:O,arrowX:ee,arrowY:et,shouldHideArrow:en,children:(0,E.jsx)(b.WV.div,{"data-side":G,"data-align":J,...k,ref:N,style:{...k.style,animation:q?void 0:"none"}})})})});tE.displayName=tw;var tS="PopperArrow",tC={top:"bottom",right:"left",bottom:"top",left:"right"},tR=f.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tx(tS,n),i=tC[o.placedSide];return(0,E.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,E.jsx)(tu,{...r,ref:t,style:{...r.style,display:"block"}})})});function tT(e){return null!==e}tR.displayName=tS;var tA=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,v]=tL(a),h={start:"0%",center:"50%",end:"100%"}[v],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?h:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function tL(e){let[t,n="center"]=e.split("-");return[t,n]}var tk=f.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[l,a]=f.useState(!1);(0,tc.b)(()=>a(!0),[]);let u=o||l&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return u?p.createPortal((0,E.jsx)(b.WV.div,{...i,ref:t}),u):null});tk.displayName="Portal";var tP=n(37053),tM=n(80886),tD=n(6718),tN=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});f.forwardRef((e,t)=>(0,E.jsx)(b.WV.span,{...e,ref:t,style:{...tN,...e.style}})).displayName="VisuallyHidden";var tj=new WeakMap,tO=new WeakMap,tW={},tI=0,tH=function(e){return e&&(e.host||tH(e.parentNode))},tF=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tH(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tW[n]||(tW[n]=new WeakMap);var i=tW[n],l=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tj.get(e)||0)+1,c=(i.get(e)||0)+1;tj.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tO.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tI++,function(){l.forEach(function(e){var t=tj.get(e)-1,o=i.get(e)-1;tj.set(e,t),i.set(e,o),t||(tO.has(e)||e.removeAttribute(r),tO.delete(e)),o||e.removeAttribute(n)}),--tI||(tj=new WeakMap,tj=new WeakMap,tO=new WeakMap,tW={})}},tV=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tF(r,o,n,"aria-hidden")):function(){return null}},tB=function(){return(tB=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function t_(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var tz="right-scroll-bar-position",tK="width-before-scroll-bar";function tY(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tU="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,tZ=new WeakMap,tX=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),l=[],a=!1,u={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:null},useMedium:function(e){var t=i(e,a);return l.push(t),function(){l=l.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;l.length;){var t=l;l=[],t.forEach(e)}l={push:function(t){return e(t)},filter:function(){return l}}},assignMedium:function(e){a=!0;var t=[];if(l.length){var n=l;l=[],n.forEach(e),t=l}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),l={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),l}}}}).options=tB({async:!0,ssr:!1},o),u),tq=function(){},t$=f.forwardRef(function(e,t){var n,r,o,i,l=f.useRef(null),a=f.useState({onScrollCapture:tq,onWheelCapture:tq,onTouchMoveCapture:tq}),u=a[0],c=a[1],s=e.forwardProps,d=e.children,p=e.className,v=e.removeScrollBar,h=e.enabled,m=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,E=e.as,S=e.gapMode,C=t_(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[l,t],r=function(e){return n.forEach(function(t){return tY(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tU(function(){var e=tZ.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tY(e,null)}),r.forEach(function(e){t.has(e)||tY(e,o)})}tZ.set(i,n)},[n]),i),T=tB(tB({},C),u);return f.createElement(f.Fragment,null,h&&f.createElement(g,{sideCar:tX,removeScrollBar:v,shards:m,noRelative:y,noIsolation:w,inert:b,setCallbacks:c,allowPinchZoom:!!x,lockRef:l,gapMode:S}),s?f.cloneElement(f.Children.only(d),tB(tB({},T),{ref:R})):f.createElement(void 0===E?"div":E,tB({},T,{className:p,ref:R}),d))});t$.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t$.classNames={fullWidth:tK,zeroRight:tz};var tG=function(e){var t=e.sideCar,n=t_(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,tB({},n))};tG.isSideCarExport=!0;var tJ=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=d||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tQ=function(){var e=tJ();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t0=function(){var e=tQ();return function(t){return e(t.styles,t.dynamic),null}},t1={left:0,top:0,right:0,gap:0},t2=function(e){return parseInt(e||"",10)||0},t6=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t2(n),t2(r),t2(o)]},t5=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t1;var t=t6(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t8=t0(),t3="data-scroll-locked",t7=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(t3,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tz," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tK," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tz," .").concat(tz," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tK," .").concat(tK," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t3,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},t9=function(){var e=parseInt(document.body.getAttribute(t3)||"0",10);return isFinite(e)?e:0},t4=function(){f.useEffect(function(){return document.body.setAttribute(t3,(t9()+1).toString()),function(){var e=t9()-1;e<=0?document.body.removeAttribute(t3):document.body.setAttribute(t3,e.toString())}},[])},ne=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t4();var i=f.useMemo(function(){return t5(o)},[o]);return f.createElement(t8,{styles:t7(i,!t,o,n?"":"!important")})},nt=!1;if("undefined"!=typeof window)try{var nn=Object.defineProperty({},"passive",{get:function(){return nt=!0,!0}});window.addEventListener("test",nn,nn),window.removeEventListener("test",nn,nn)}catch(e){nt=!1}var nr=!!nt&&{passive:!1},no=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},ni=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nl(e,r)){var o=na(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nl=function(e,t){return"v"===e?no(t,"overflowY"):no(t,"overflowX")},na=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nu=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=na(e,u),h=v[0],m=v[1]-v[2]-l*h;(h||m)&&nl(e,u)&&(f+=m,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},nc=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ns=function(e){return[e.deltaX,e.deltaY]},nd=function(e){return e&&"current"in e?e.current:e},nf=0,np=[],nv=(c=function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(nf++)[0],i=f.useState(t0)[0],l=f.useRef(e);f.useEffect(function(){l.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nd),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=nc(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=ni(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ni(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nu(p,t,e,"h"===p?u:c,!0)},[]),u=f.useCallback(function(e){if(np.length&&np[np.length-1]===i){var n="deltaY"in e?ns(e):nc(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(nd).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?a(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=f.useCallback(function(e){n.current=nc(e),r.current=void 0},[]),d=f.useCallback(function(t){c(t.type,ns(t),t.target,a(t,e.lockRef.current))},[]),p=f.useCallback(function(t){c(t.type,nc(t),t.target,a(t,e.lockRef.current))},[]);f.useEffect(function(){return np.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,nr),document.addEventListener("touchmove",u,nr),document.addEventListener("touchstart",s,nr),function(){np=np.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,nr),document.removeEventListener("touchmove",u,nr),document.removeEventListener("touchstart",s,nr)}},[]);var v=e.removeScrollBar,h=e.inert;return f.createElement(f.Fragment,null,h?f.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?f.createElement(ne,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tX.useMedium(c),tG),nh=f.forwardRef(function(e,t){return f.createElement(t$,tB({},e,{ref:t,sideCar:nv}))});nh.classNames=t$.classNames;var nm=[" ","Enter","ArrowUp","ArrowDown"],ng=[" ","Enter"],ny="Select",[nw,nb,nx]=(0,m.B)(ny),[nE,nS]=(0,y.b)(ny,[nx,tp]),nC=tp(),[nR,nT]=nE(ny),[nA,nL]=nE(ny),nk=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:a,onValueChange:u,dir:c,name:s,autoComplete:d,disabled:p,required:v,form:h}=e,m=nC(t),[g,y]=f.useState(null),[b,x]=f.useState(null),[S,C]=f.useState(!1),R=(0,w.gm)(c),[T,A]=(0,tM.T)({prop:r,defaultProp:null!=o&&o,onChange:i,caller:ny}),[L,k]=(0,tM.T)({prop:l,defaultProp:a,onChange:u,caller:ny}),P=f.useRef(null),M=!g||h||!!g.closest("form"),[D,N]=f.useState(new Set),j=Array.from(D).map(e=>e.props.value).join(";");return(0,E.jsx)(tm,{...m,children:(0,E.jsxs)(nR,{required:v,scope:t,trigger:g,onTriggerChange:y,valueNode:b,onValueNodeChange:x,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,F.M)(),value:L,onValueChange:k,open:T,onOpenChange:A,dir:R,triggerPointerDownPosRef:P,disabled:p,children:[(0,E.jsx)(nw.Provider,{scope:t,children:(0,E.jsx)(nA,{scope:e.__scopeSelect,onNativeOptionAdd:f.useCallback(e=>{N(t=>new Set(t).add(e))},[]),onNativeOptionRemove:f.useCallback(e=>{N(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,E.jsxs)(ri,{"aria-hidden":!0,required:v,tabIndex:-1,name:s,autoComplete:d,value:L,onChange:e=>k(e.target.value),disabled:p,form:h,children:[void 0===L?(0,E.jsx)("option",{value:""}):null,Array.from(D)]},j):null]})})};nk.displayName=ny;var nP="SelectTrigger",nM=f.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nC(n),l=nT(nP,n),a=l.disabled||r,u=(0,g.e)(t,l.onTriggerChange),c=nb(n),s=f.useRef("touch"),[d,p,v]=ra(e=>{let t=c().filter(e=>!e.disabled),n=t.find(e=>e.value===l.value),r=ru(t,e,n);void 0!==r&&l.onValueChange(r.value)}),m=e=>{a||(l.onOpenChange(!0),v()),e&&(l.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,E.jsx)(ty,{asChild:!0,...i,children:(0,E.jsx)(b.WV.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":rl(l.value)?"":void 0,...o,ref:u,onClick:(0,h.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==s.current&&m(e)}),onPointerDown:(0,h.M)(o.onPointerDown,e=>{s.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(m(e),e.preventDefault())}),onKeyDown:(0,h.M)(o.onKeyDown,e=>{let t=""!==d.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),(!t||" "!==e.key)&&nm.includes(e.key)&&(m(),e.preventDefault())})})})});nM.displayName=nP;var nD="SelectValue",nN=f.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nT(nD,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,d=(0,g.e)(t,u.onValueNodeChange);return(0,tc.b)(()=>{c(s)},[c,s]),(0,E.jsx)(b.WV.span,{...a,ref:d,style:{pointerEvents:"none"},children:rl(u.value)?(0,E.jsx)(E.Fragment,{children:l}):i})});nN.displayName=nD;var nj=f.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,E.jsx)(b.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nj.displayName="SelectIcon";var nO=e=>(0,E.jsx)(tk,{asChild:!0,...e});nO.displayName="SelectPortal";var nW="SelectContent",nI=f.forwardRef((e,t)=>{let n=nT(nW,e.__scopeSelect),[r,o]=f.useState();return((0,tc.b)(()=>{o(new DocumentFragment)},[]),n.open)?(0,E.jsx)(nB,{...e,ref:t}):r?p.createPortal((0,E.jsx)(nH,{scope:e.__scopeSelect,children:(0,E.jsx)(nw.Slot,{scope:e.__scopeSelect,children:(0,E.jsx)("div",{children:e.children})})}),r):null});nI.displayName=nW;var[nH,nF]=nE(nW),nV=(0,tP.Z8)("SelectContent.RemoveScroll"),nB=f.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:a,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:v,sticky:m,hideWhenDetached:y,avoidCollisions:w,...b}=e,x=nT(nW,n),[S,C]=f.useState(null),[T,A]=f.useState(null),P=(0,g.e)(t,e=>C(e)),[M,D]=f.useState(null),[j,O]=f.useState(null),W=nb(n),[I,H]=f.useState(!1),F=f.useRef(!1);f.useEffect(()=>{if(S)return tV(S)},[S]),f.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:k()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:k()),L++,()=>{1===L&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),L--}},[]);let V=f.useCallback(e=>{let[t,...n]=W().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[W,T]),B=f.useCallback(()=>V([M,S]),[V,M,S]);f.useEffect(()=>{I&&B()},[I,B]);let{onOpenChange:_,triggerPointerDownPosRef:z}=x;f.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=z.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=z.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||_(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,_,z]),f.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[K,Y]=ra(e=>{let t=W().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=ru(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),U=f.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==x.value&&x.value===t||r)&&(D(e),r&&(F.current=!0))},[x.value]),Z=f.useCallback(()=>null==S?void 0:S.focus(),[S]),X=f.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==x.value&&x.value===t||r)&&O(e)},[x.value]),q="popper"===r?nz:n_,$=q===nz?{side:a,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:v,sticky:m,hideWhenDetached:y,avoidCollisions:w}:{};return(0,E.jsx)(nH,{scope:n,content:S,viewport:T,onViewportChange:A,itemRefCallback:U,selectedItem:M,onItemLeave:Z,itemTextRefCallback:X,focusSelectedItem:B,selectedItemText:j,position:r,isPositioned:I,searchRef:K,children:(0,E.jsx)(nh,{as:nV,allowPinchZoom:!0,children:(0,E.jsx)(N,{asChild:!0,trapped:x.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,h.M)(o,e=>{var t;null===(t=x.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,E.jsx)(R,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:(0,E.jsx)(q,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:e=>e.preventDefault(),...b,...$,onPlaced:()=>H(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:(0,h.M)(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=W().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>V(t)),e.preventDefault()}})})})})})})});nB.displayName="SelectContentImpl";var n_=f.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nT(nW,n),l=nF(nW,n),[a,u]=f.useState(null),[c,s]=f.useState(null),d=(0,g.e)(t,e=>s(e)),p=nb(n),h=f.useRef(!1),m=f.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:x,focusSelectedItem:S}=l,C=f.useCallback(()=>{if(i.trigger&&i.valueNode&&a&&c&&y&&w&&x){let e=i.trigger.getBoundingClientRect(),t=c.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=x.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,u=e.width+l,c=Math.max(u,t.width),s=v(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.left=s+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,u=e.width+l,c=Math.max(u,t.width),s=v(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.right=s+"px"}let l=p(),u=window.innerHeight-20,s=y.scrollHeight,d=window.getComputedStyle(c),f=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),b=f+m+s+parseInt(d.paddingBottom,10)+g,E=Math.min(5*w.offsetHeight,b),S=window.getComputedStyle(y),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,A=w.offsetHeight/2,L=f+m+(w.offsetTop+A);if(L<=T){let e=l.length>0&&w===l[l.length-1].ref.current;a.style.bottom="0px";let t=c.clientHeight-y.offsetTop-y.offsetHeight;a.style.height=L+Math.max(u-T,A+(e?R:0)+t+g)+"px"}else{let e=l.length>0&&w===l[0].ref.current;a.style.top="0px";let t=Math.max(T,f+y.offsetTop+(e?C:0)+A);a.style.height=t+(b-L)+"px",y.scrollTop=L-T+y.offsetTop}a.style.margin="".concat(10,"px 0"),a.style.minHeight=E+"px",a.style.maxHeight=u+"px",null==r||r(),requestAnimationFrame(()=>h.current=!0)}},[p,i.trigger,i.valueNode,a,c,y,w,x,i.dir,r]);(0,tc.b)(()=>C(),[C]);let[R,T]=f.useState();(0,tc.b)(()=>{c&&T(window.getComputedStyle(c).zIndex)},[c]);let A=f.useCallback(e=>{e&&!0===m.current&&(C(),null==S||S(),m.current=!1)},[C,S]);return(0,E.jsx)(nK,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:h,onScrollButtonChange:A,children:(0,E.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,E.jsx)(b.WV.div,{...o,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});n_.displayName="SelectItemAlignedPosition";var nz=f.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nC(n);return(0,E.jsx)(tE,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nz.displayName="SelectPopperPosition";var[nK,nY]=nE(nW,{}),nU="SelectViewport",nZ=f.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nF(nU,n),l=nY(nU,n),a=(0,g.e)(t,i.onViewportChange),u=f.useRef(0);return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,E.jsx)(nw.Slot,{scope:n,children:(0,E.jsx)(b.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,h.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=l;if((null==r?void 0:r.current)&&n){let e=Math.abs(u.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});nZ.displayName=nU;var nX="SelectGroup",[nq,n$]=nE(nX),nG=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,F.M)();return(0,E.jsx)(nq,{scope:n,id:o,children:(0,E.jsx)(b.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nG.displayName=nX;var nJ="SelectLabel",nQ=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=n$(nJ,n);return(0,E.jsx)(b.WV.div,{id:o.id,...r,ref:t})});nQ.displayName=nJ;var n0="SelectItem",[n1,n2]=nE(n0),n6=f.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,a=nT(n0,n),u=nF(n0,n),c=a.value===r,[s,d]=f.useState(null!=i?i:""),[p,v]=f.useState(!1),m=(0,g.e)(t,e=>{var t;return null===(t=u.itemRefCallback)||void 0===t?void 0:t.call(u,e,r,o)}),y=(0,F.M)(),w=f.useRef("touch"),x=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,E.jsx)(n1,{scope:n,value:r,disabled:o,textId:y,isSelected:c,onItemTextChange:f.useCallback(e=>{d(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,E.jsx)(nw.ItemSlot,{scope:n,value:r,disabled:o,textValue:s,children:(0,E.jsx)(b.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":p?"":void 0,"aria-selected":c&&p,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:m,onFocus:(0,h.M)(l.onFocus,()=>v(!0)),onBlur:(0,h.M)(l.onBlur,()=>v(!1)),onClick:(0,h.M)(l.onClick,()=>{"mouse"!==w.current&&x()}),onPointerUp:(0,h.M)(l.onPointerUp,()=>{"mouse"===w.current&&x()}),onPointerDown:(0,h.M)(l.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,h.M)(l.onPointerMove,e=>{if(w.current=e.pointerType,o){var t;null===(t=u.onItemLeave)||void 0===t||t.call(u)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,h.M)(l.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=u.onItemLeave)||void 0===t||t.call(u)}}),onKeyDown:(0,h.M)(l.onKeyDown,e=>{var t;(null===(t=u.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(ng.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});n6.displayName=n0;var n5="SelectItemText",n8=f.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,l=nT(n5,n),a=nF(n5,n),u=n2(n5,n),c=nL(n5,n),[s,d]=f.useState(null),v=(0,g.e)(t,e=>d(e),u.onItemTextChange,e=>{var t;return null===(t=a.itemTextRefCallback)||void 0===t?void 0:t.call(a,e,u.value,u.disabled)}),h=null==s?void 0:s.textContent,m=f.useMemo(()=>(0,E.jsx)("option",{value:u.value,disabled:u.disabled,children:h},u.value),[u.disabled,u.value,h]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=c;return(0,tc.b)(()=>(y(m),()=>w(m)),[y,w,m]),(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(b.WV.span,{id:u.textId,...i,ref:v}),u.isSelected&&l.valueNode&&!l.valueNodeHasChildren?p.createPortal(i.children,l.valueNode):null]})});n8.displayName=n5;var n3="SelectItemIndicator",n7=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return n2(n3,n).isSelected?(0,E.jsx)(b.WV.span,{"aria-hidden":!0,...r,ref:t}):null});n7.displayName=n3;var n9="SelectScrollUpButton",n4=f.forwardRef((e,t)=>{let n=nF(n9,e.__scopeSelect),r=nY(n9,e.__scopeSelect),[o,i]=f.useState(!1),l=(0,g.e)(t,r.onScrollButtonChange);return(0,tc.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,E.jsx)(rn,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});n4.displayName=n9;var re="SelectScrollDownButton",rt=f.forwardRef((e,t)=>{let n=nF(re,e.__scopeSelect),r=nY(re,e.__scopeSelect),[o,i]=f.useState(!1),l=(0,g.e)(t,r.onScrollButtonChange);return(0,tc.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,E.jsx)(rn,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});rt.displayName=re;var rn=f.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nF("SelectScrollButton",n),l=f.useRef(null),a=nb(n),u=f.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return f.useEffect(()=>()=>u(),[u]),(0,tc.b)(()=>{var e;let t=a().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[a]),(0,E.jsx)(b.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,h.M)(o.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(r,50))}),onPointerMove:(0,h.M)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===l.current&&(l.current=window.setInterval(r,50))}),onPointerLeave:(0,h.M)(o.onPointerLeave,()=>{u()})})}),rr=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,E.jsx)(b.WV.div,{"aria-hidden":!0,...r,ref:t})});rr.displayName="SelectSeparator";var ro="SelectArrow";f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nC(n),i=nT(ro,n),l=nF(ro,n);return i.open&&"popper"===l.position?(0,E.jsx)(tR,{...o,...r,ref:t}):null}).displayName=ro;var ri=f.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,i=f.useRef(null),l=(0,g.e)(t,i),a=(0,tD.D)(r);return f.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,E.jsx)(b.WV.select,{...o,style:{...tN,...o.style},ref:l,defaultValue:r})});function rl(e){return""===e||void 0===e}function ra(e){let t=(0,x.W)(e),n=f.useRef(""),r=f.useRef(0),o=f.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=f.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return f.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function ru(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}ri.displayName="SelectBubbleInput";var rc=nk,rs=nM,rd=nN,rf=nj,rp=nO,rv=nI,rh=nZ,rm=nG,rg=nQ,ry=n6,rw=n8,rb=n7,rx=n4,rE=rt,rS=rr},6718:function(e,t,n){n.d(t,{D:function(){return o}});var r=n(2265);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},90420:function(e,t,n){n.d(t,{t:function(){return i}});var r=n(2265),o=n(61188);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);