(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9160],{80318:function(e,t,r){Promise.resolve().then(r.bind(r,32950))},32950:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return d}});var n=r(57437),a=r(62869),s=r(66070);let l=(0,r(39763).Z)("FileQuestion",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["path",{d:"M10 10.3c.2-.4.5-.8.9-1a2.1 2.1 0 0 1 2.6.4c.3.4.5.8.5 1.3 0 1.3-2 2-2 2",key:"1umxtm"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var i=r(99637),c=r(32660),o=r(27648);function d(){return(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,n.jsxs)(s.Zb,{className:"w-full max-w-md",children:[(0,n.jsxs)(s.Ol,{className:"text-center",children:[(0,n.jsx)("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:(0,n.jsx)(l,{className:"h-6 w-6 text-blue-600"})}),(0,n.jsx)(s.ll,{className:"text-xl font-bold text-gray-900",children:"الصفحة غير موجودة"})]}),(0,n.jsxs)(s.aY,{className:"space-y-4",children:[(0,n.jsx)("p",{className:"text-gray-600 text-center",children:"عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها."}),(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)(o.default,{href:"/",className:"w-full",children:(0,n.jsxs)(a.z,{className:"w-full",children:[(0,n.jsx)(i.Z,{className:"h-4 w-4 ml-2"}),"العودة للصفحة الرئيسية"]})}),(0,n.jsxs)(a.z,{variant:"outline",onClick:()=>window.history.back(),className:"w-full",children:[(0,n.jsx)(c.Z,{className:"h-4 w-4 ml-2"}),"العودة للصفحة السابقة"]})]})]})]})})}},62869:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var n=r(57437),a=r(2265),s=r(37053),l=r(90535),i=r(94508);let c=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:o=!1,...d}=e,u=o?s.g7:"button";return(0,n.jsx)(u,{className:(0,i.cn)(c({variant:a,size:l,className:r})),ref:t,...d})});o.displayName="Button"},66070:function(e,t,r){"use strict";r.d(t,{Ol:function(){return i},SZ:function(){return o},Zb:function(){return l},aY:function(){return d},ll:function(){return c}});var n=r(57437),a=r(2265),s=r(94508);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},94508:function(e,t,r){"use strict";r.d(t,{cn:function(){return s}});var n=r(61994),a=r(53335);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}},32660:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},99637:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])}},function(e){e.O(0,[5554,5650,2971,2117,1744],function(){return e(e.s=80318)}),_N_E=e.O()}]);