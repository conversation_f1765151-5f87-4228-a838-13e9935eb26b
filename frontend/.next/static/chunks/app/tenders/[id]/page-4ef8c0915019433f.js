(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4678],{21347:function(e,s,a){Promise.resolve().then(a.bind(a,94920))},94920:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return O}});var t=a(57437),l=a(2265),i=a(99376),r=a(66070),n=a(62869),c=a(35974),d=a(6512),x=a(24895),m=a(2014),h=a(29116),o=a(22252),j=a(32660),u=a(88997),g=a(68919),N=a(94630),v=a(18930),p=a(42208),f=a(31047),y=a(65302),w=a(48736),b=a(92735),Z=a(44743),z=a(91723),_=a(62720),S=a(83774),C=a(95805),k=a(60044);function O(){var e,s;let a;let O=(0,i.useParams)(),D=(0,i.useRouter)(),{toast:F}=(0,x.p)(),[Y,E]=(0,l.useState)(null),[R,A]=(0,l.useState)(!0),[P,q]=(0,l.useState)(!1),[I,L]=(0,l.useState)(null),[M,T]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=localStorage.getItem("user");e&&L(JSON.parse(e)),J()},[O.id]);let J=async()=>{try{A(!0);let e=await h.ZP.get("/tenders/".concat(O.id));if(e.data.success){if(E(e.data.data),I&&e.data.data.applications){let s=e.data.data.applications.find(e=>e.applicant._id===I._id);T(!!s)}}else F({title:"خطأ",description:"لم يتم العثور على المناقصة",variant:"destructive"}),D.push("/tenders")}catch(e){console.error("Error loading tender:",e),F({title:"خطأ في التحميل",description:"حدث خطأ في تحميل بيانات المناقصة",variant:"destructive"}),D.push("/tenders")}finally{A(!1)}},B=async()=>{if(!I){F({title:"تسجيل الدخول مطلوب",description:"يجب تسجيل الدخول للتقدم للمناقصة",variant:"destructive"});return}try{q(!0),(await h.ZP.post("/tenders/".concat(O.id,"/apply"))).data.success&&(F({title:"تم التقديم",description:"تم تقديم طلبك للمناقصة بنجاح"}),T(!0),J())}catch(a){var e,s;F({title:"خطأ في التقديم",description:(null===(s=a.response)||void 0===s?void 0:null===(e=s.data)||void 0===e?void 0:e.message)||"حدث خطأ في تقديم الطلب",variant:"destructive"})}finally{q(!1)}},G=I&&Y&&I._id===Y.organizer._id,H=I&&Y&&I._id!==Y.organizer._id&&"active"===Y.status&&!M;return R?(0,t.jsx)(m.default,{allowedRoles:["user","company","government","admin"],children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"جاري تحميل تفاصيل المناقصة..."})]})})}):Y?(0,t.jsx)(m.default,{allowedRoles:["user","company","government","admin"],children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n.z,{variant:"ghost",onClick:()=>D.push("/tenders"),className:"flex items-center gap-2",children:[(0,t.jsx)(j.Z,{className:"h-4 w-4"}),"العودة للمناقصات"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(n.z,{variant:"outline",children:[(0,t.jsx)(u.Z,{className:"h-4 w-4 ml-2"}),"إضافة للمفضلة"]}),(0,t.jsxs)(n.z,{variant:"outline",children:[(0,t.jsx)(g.Z,{className:"h-4 w-4 ml-2"}),"مشاركة"]}),G&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(n.z,{variant:"outline",onClick:()=>D.push("/tenders/".concat(Y._id,"/edit")),children:[(0,t.jsx)(N.Z,{className:"h-4 w-4 ml-2"}),"تعديل"]}),(0,t.jsxs)(n.z,{variant:"destructive",children:[(0,t.jsx)(v.Z,{className:"h-4 w-4 ml-2"}),"حذف"]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)(r.Zb,{children:(0,t.jsx)(r.Ol,{children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(r.ll,{className:"text-2xl",children:Y.title}),(0,t.jsxs)("div",{className:"flex items-center gap-4 mt-3",children:[(e=>{switch(e){case"active":return(0,t.jsx)(c.C,{className:"bg-green-100 text-green-800",children:"نشط"});case"completed":return(0,t.jsx)(c.C,{className:"bg-blue-100 text-blue-800",children:"مكتمل"});case"review":return(0,t.jsx)(c.C,{className:"bg-yellow-100 text-yellow-800",children:"قيد المراجعة"});case"cancelled":return(0,t.jsx)(c.C,{variant:"destructive",children:"ملغي"});default:return(0,t.jsx)(c.C,{variant:"outline",children:e})}})(Y.status),(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-600",children:[(0,t.jsx)(p.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[Y.viewsCount," مشاهدة"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-600",children:[(0,t.jsx)(f.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:new Date(Y.createdAt).toLocaleDateString("ar-SA")})]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-green-600",children:(a=Y.budget,new Intl.NumberFormat("ar-SA",{style:"currency",currency:"SAR",minimumFractionDigits:0}).format(a))}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"الميزانية المخصصة"})]})]})})}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsx)(r.Ol,{children:(0,t.jsx)(r.ll,{children:"وصف المناقصة"})}),(0,t.jsx)(r.aY,{children:(0,t.jsx)("p",{className:"text-gray-700 leading-relaxed whitespace-pre-line",children:Y.description})})]}),Y.requirements&&Y.requirements.length>0&&(0,t.jsxs)(r.Zb,{children:[(0,t.jsx)(r.Ol,{children:(0,t.jsx)(r.ll,{children:"المتطلبات والشروط"})}),(0,t.jsx)(r.aY,{children:(0,t.jsx)("ul",{className:"space-y-2",children:Y.requirements.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.Z,{className:"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-gray-700",children:e})]},s))})})]}),Y.documents&&Y.documents.length>0&&(0,t.jsxs)(r.Zb,{children:[(0,t.jsx)(r.Ol,{children:(0,t.jsx)(r.ll,{children:"المستندات المرفقة"})}),(0,t.jsx)(r.aY,{children:(0,t.jsx)("div",{className:"space-y-2",children:Y.documents.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(w.Z,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("span",{className:"font-medium",children:["مستند ",s+1]})]}),(0,t.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,t.jsx)(b.Z,{className:"h-4 w-4 ml-2"}),"تحميل"]})]},s))})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[H&&(0,t.jsx)(r.Zb,{children:(0,t.jsx)(r.aY,{className:"p-6",children:(0,t.jsx)(n.z,{onClick:B,disabled:P,className:"w-full h-12 text-lg bg-green-600 hover:bg-green-700",children:P?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),"جاري التقديم..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Z.Z,{className:"h-5 w-5 ml-2"}),"تقدم للمناقصة"]})})})}),M&&(0,t.jsx)(r.Zb,{className:"border-green-200 bg-green-50",children:(0,t.jsxs)(r.aY,{className:"p-6 text-center",children:[(0,t.jsx)(y.Z,{className:"h-12 w-12 text-green-600 mx-auto mb-3"}),(0,t.jsx)("h3",{className:"font-medium text-green-900 mb-2",children:"تم التقديم"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"لقد تقدمت لهذه المناقصة بالفعل"})]})}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsx)(r.Ol,{children:(0,t.jsx)(r.ll,{children:"معلومات المناقصة"})}),(0,t.jsxs)(r.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(z.Z,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"الموعد النهائي"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:new Date(Y.deadline).toLocaleDateString("ar-SA")}),(0,t.jsx)("div",{className:"text-sm text-orange-600 font-medium",children:(e=>{let s=new Date,a=new Date(e).getTime()-s.getTime();if(a<=0)return"انتهى";let t=Math.floor(a/864e5),l=Math.floor(a%864e5/36e5);return t>0?"".concat(t," يوم ").concat(l," ساعة"):l>0?"".concat(l," ساعة"):"أقل من ساعة"})(Y.deadline)})]})]}),(0,t.jsx)(d.Z,{}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(_.Z,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"الفئة"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:{construction:"إنشاءات",it_technology:"تقنية المعلومات",consulting:"استشارات",healthcare:"رعاية صحية",education:"تعليم",transportation:"نقل ومواصلات",other:"أخرى"}[s=Y.category]||s})]})]}),(0,t.jsx)(d.Z,{}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(S.Z,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"الموقع"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:Y.location})]})]}),(0,t.jsx)(d.Z,{}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(C.Z,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"عدد المتقدمين"}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(null===(e=Y.applications)||void 0===e?void 0:e.length)||0," متقدم"]})]})]})]})]}),(0,t.jsxs)(r.Zb,{children:[(0,t.jsx)(r.Ol,{children:(0,t.jsx)(r.ll,{children:"الجهة المنظمة"})}),(0,t.jsxs)(r.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(k.Z,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:Y.organizer.profile.governmentEntity}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"جهة حكومية"})]})]}),Y.organizer.profile.phone&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Z,{}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("div",{className:"font-medium text-gray-600",children:"الهاتف"}),(0,t.jsx)("div",{children:Y.organizer.profile.phone})]})]}),Y.organizer.profile.email&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Z,{}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("div",{className:"font-medium text-gray-600",children:"البريد الإلكتروني"}),(0,t.jsx)("div",{children:Y.organizer.profile.email})]})]})]})]})]})]})]})}):(0,t.jsx)(m.default,{allowedRoles:["user","company","government","admin"],children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(o.Z,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"المناقصة غير موجودة"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"لم يتم العثور على المناقصة المطلوبة"}),(0,t.jsx)(n.z,{onClick:()=>D.push("/tenders"),children:"العودة للمناقصات"})]})})}},6512:function(e,s,a){"use strict";a.d(s,{Z:function(){return n}});var t=a(57437),l=a(2265),i=a(55156),r=a(94508);let n=l.forwardRef((e,s)=>{let{className:a,orientation:l="horizontal",decorative:n=!0,...c}=e;return(0,t.jsx)(i.f,{ref:s,decorative:n,orientation:l,className:(0,r.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",a),...c})});n.displayName=i.f.displayName}},function(e){e.O(0,[5554,3464,5650,3817,3119,4057,2971,2117,1744],function(){return e(e.s=21347)}),_N_E=e.O()}]);