(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6410],{16100:function(e,t,a){Promise.resolve().then(a.bind(a,96353))},96353:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return m}});var s=a(57437),r=a(2265),n=a(66070),l=a(2014),i=a(30401),d=a(94766),c=a(18930),o=a(62869),u=a(68680);function m(){let[e,t]=(0,r.useState)([]),[a,m]=(0,r.useState)(0),[x,f]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=localStorage.getItem("token");if(!e)return;let a=(0,u.io)("http://localhost:5000/api",{auth:{token:e},reconnection:!0,reconnectionAttempts:5,reconnectionDelay:1e3});return f(a),a.on("notification",e=>{let a={id:Date.now().toString(),title:e.title,message:e.message,type:e.type||"notification",isRead:!1,timestamp:new Date,data:e.data};t(e=>[a,...e]),m(e=>e+1)}),a.on("auction_update",e=>{let a={id:Date.now().toString(),title:"تحديث المزاد",message:e.message||"تم تحديث المزاد",type:"auction_update",isRead:!1,timestamp:new Date,data:e};t(e=>[a,...e]),m(e=>e+1)}),a.on("bid_placed",e=>{let a={id:Date.now().toString(),title:"مزايدة جديدة",message:e.message||"تم وضع مزايدة جديدة",type:"bid_placed",isRead:!1,timestamp:new Date,data:e};t(e=>[a,...e]),m(e=>e+1)}),a.on("tender_update",e=>{let a={id:Date.now().toString(),title:"تحديث المناقصة",message:e.message||"تم تحديث المناقصة",type:"tender_update",isRead:!1,timestamp:new Date,data:e};t(e=>[a,...e]),m(e=>e+1)}),()=>{a.disconnect()}},[]);let h=e=>{t(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),m(e=>Math.max(0,e-1))},p=a=>{let s=e.find(e=>e.id===a);s&&!s.isRead&&m(e=>Math.max(0,e-1)),t(e=>e.filter(e=>e.id!==a))},g=e=>{switch(e){case"auction_update":return"\uD83C\uDFF7️";case"bid_placed":return"\uD83D\uDCB0";case"tender_update":return"\uD83D\uDCCB";default:return"\uD83D\uDD14"}},j=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/6e4);if(t<1)return"الآن";if(t<60)return"منذ ".concat(t," دقيقة");let a=Math.floor(t/60);return a<24?"منذ ".concat(a," ساعة"):"منذ ".concat(Math.floor(a/24)," يوم")};return(0,s.jsx)(l.default,{allowedRoles:["individual","company","government"],children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("header",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:["الإشعارات",a>0&&(0,s.jsx)("span",{className:"bg-red-500 text-white text-sm px-2 py-1 rounded-full",children:a})]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"تابع آخر تنبيهاتك وإشعاراتك هنا"})]}),e.length>0&&(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>{t(e=>e.map(e=>({...e,isRead:!0}))),m(0)},disabled:0===a,children:[(0,s.jsx)(i.Z,{className:"h-4 w-4 mr-2"}),"قراءة الكل"]})})]}),(0,s.jsxs)(n.Zb,{children:[(0,s.jsx)(n.Ol,{children:(0,s.jsx)(n.ll,{children:"الإشعارات المباشرة"})}),(0,s.jsx)(n.aY,{children:0===e.length?(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)(d.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"لا توجد إشعارات حالياً"}),(0,s.jsx)("p",{className:"text-sm",children:"ستظهر الإشعارات الجديدة هنا تلقائياً"})]}):(0,s.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,s.jsx)("div",{className:"p-4 border rounded-lg transition-all duration-200 ".concat(e.isRead?"bg-gray-50 border-gray-200":"bg-blue-50 border-blue-200 shadow-sm"),children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3 flex-1",children:[(0,s.jsx)("span",{className:"text-2xl",children:g(e.type)}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.message}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:j(e.timestamp)})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[!e.isRead&&(0,s.jsx)(o.z,{variant:"ghost",size:"sm",onClick:()=>h(e.id),className:"text-blue-600 hover:text-blue-800",children:(0,s.jsx)(i.Z,{className:"h-4 w-4"})}),(0,s.jsx)(o.z,{variant:"ghost",size:"sm",onClick:()=>p(e.id),className:"text-red-600 hover:text-red-800",children:(0,s.jsx)(c.Z,{className:"h-4 w-4"})})]})]})},e.id))})})]})]})})}},2014:function(e,t,a){"use strict";a.d(t,{default:function(){return i}});var s=a(57437),r=a(2265),n=a(99376),l=a(63119);function i(e){let{children:t,allowedRoles:a}=e,[i,d]=(0,r.useState)(null),[c,o]=(0,r.useState)(!0),u=(0,n.useRouter)();return((0,r.useEffect)(()=>{let e=localStorage.getItem("token"),t=localStorage.getItem("user");if(!e||!t){u.push("/auth/login");return}let s=JSON.parse(t);if(!a.includes(s.role)){switch(s.role){case"admin":case"super_admin":u.push("/admin/dashboard");break;case"company":u.push("/company/dashboard");break;case"individual":u.push("/user/dashboard");break;case"government":u.push("/government/dashboard");break;default:u.push("/auth/login")}return}if("admin"!==s.role&&"super_admin"!==s.role&&"approved"!==s.status){u.push("/account-status");return}d(s),o(!1)},[u,a]),c)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-muted-foreground",children:"جاري التحميل..."})]})}):i?(0,s.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,s.jsx)(l.Z,{userRole:i.role}),(0,s.jsx)("main",{className:"flex-1 overflow-auto",children:(0,s.jsx)("div",{className:"p-6",children:t})})]}):null}},66070:function(e,t,a){"use strict";a.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return l},aY:function(){return o},ll:function(){return d}});var s=a(57437),r=a(2265),n=a(94508);let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});l.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...r})});i.displayName="CardHeader";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});d.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...r})});c.displayName="CardDescription";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...r})});o.displayName="CardContent",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"}},function(e){e.O(0,[5554,5650,2152,3119,2971,2117,1744],function(){return e(e.s=16100)}),_N_E=e.O()}]);