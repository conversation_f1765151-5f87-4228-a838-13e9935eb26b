(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1543],{67055:function(e,t,s){Promise.resolve().then(s.bind(s,49760))},49760:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return A}});var a=s(57437),r=s(2265),n=s(66070),i=s(35974),c=s(62869),l=s(16831),o=s(98617),d=s(49474),u=s(66142),m=s(33327),x=s(86595),p=s(58896),f=s(82431),h=s(31047),g=s(42488),v=s(70525),j=s(92369),N=s(24895),b=s(29116);let y=e=>{switch(e){case"Champion":return(0,a.jsx)(o.Z,{className:"h-4 w-4"});case"Master":return(0,a.jsx)(d.Z,{className:"h-4 w-4"});case"Expert":return(0,a.jsx)(u.Z,{className:"h-4 w-4"});case"Pro":return(0,a.jsx)(m.Z,{className:"h-4 w-4"});case"Active":return(0,a.jsx)(x.Z,{className:"h-4 w-4"});default:return(0,a.jsx)(p.Z,{className:"h-4 w-4"})}},w=e=>{switch(e){case"Champion":return"bg-yellow-500 text-white";case"Master":return"bg-purple-500 text-white";case"Expert":return"bg-blue-500 text-white";case"Pro":return"bg-green-500 text-white";case"Active":return"bg-orange-500 text-white";default:return"bg-gray-500 text-white"}},Z=e=>1===e?(0,a.jsx)(d.Z,{className:"h-6 w-6 text-yellow-500"}):2===e?(0,a.jsx)(u.Z,{className:"h-6 w-6 text-gray-400"}):3===e?(0,a.jsx)(m.Z,{className:"h-6 w-6 text-amber-600"}):(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-600",children:["#",e]});function A(){let[e,t]=(0,r.useState)([]),[s,o]=(0,r.useState)(!0),[u,m]=(0,r.useState)(null),[x,A]=(0,r.useState)(!1),[k,C]=(0,r.useState)("all-time"),[S,E]=(0,r.useState)(null),{toast:R}=(0,N.p)(),B=()=>[{_id:"1",user:{profile:{fullName:"أحمد محمد",avatarUrl:""},email:"<EMAIL>"},totalBids:45,totalAmount:125e3,wonAuctions:12,points:890,rank:1,badge:"Champion"},{_id:"2",user:{profile:{fullName:"فاطمة علي",avatarUrl:""},email:"<EMAIL>"},totalBids:38,totalAmount:98e3,wonAuctions:9,points:720,rank:2,badge:"Master"},{_id:"3",user:{profile:{fullName:"محمد سالم",avatarUrl:""},email:"<EMAIL>"},totalBids:32,totalAmount:76e3,wonAuctions:7,points:650,rank:3,badge:"Expert"}],P=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:k;try{let s=await b.ZP.get("/users/leaderboard?period=".concat(e,"&limit=50"));if(s.data.success)t(s.data.data.leaderboard),m(null);else throw Error("فشل في تحميل لوحة الصدارة")}catch(e){console.error("Leaderboard error:",e),m(e instanceof Error?e.message:"حدث خطأ غير متوقع"),R({title:"خطأ في التحميل",description:"حدث خطأ في تحميل بيانات المتصدرين",variant:"destructive"}),t(B())}finally{o(!1),A(!1)}},_=()=>{A(!0),P()},z=e=>{C(e),o(!0),P(e)},F=e=>{switch(e){case"daily":return"اليوم";case"weekly":return"الأسبوع";case"monthly":return"الشهر";case"all-time":return"جميع الأوقات"}};return((0,r.useEffect)(()=>{P()},[]),s)?(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(f.Z,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-primary"}),(0,a.jsx)("p",{className:"text-gray-600",children:"جاري تحميل لوحة الصدارة..."})]})})}):u?(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)(n.Zb,{className:"max-w-md mx-auto",children:(0,a.jsx)(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"خطأ في تحميل البيانات"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:u}),(0,a.jsxs)(c.z,{onClick:_,className:"w-full",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"إعادة المحاولة"]})]})})})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,a.jsx)(d.Z,{className:"h-8 w-8 text-primary mr-3"}),"لوحة الصدارة"]}),(0,a.jsxs)("p",{className:"text-gray-600 mt-2",children:["تصنيف أفضل المزايدين في المنصة - ",F(k)]})]}),(0,a.jsxs)(c.z,{onClick:_,disabled:x,variant:"outline",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2 ".concat(x?"animate-spin":"")}),"تحديث"]})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(h.Z,{className:"h-5 w-5 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 ml-3",children:"الفترة الزمنية:"}),(0,a.jsx)("div",{className:"flex space-x-2 space-x-reverse",children:["daily","weekly","monthly","all-time"].map(e=>(0,a.jsx)(c.z,{variant:k===e?"default":"outline",size:"sm",onClick:()=>z(e),disabled:s,children:F(e)},e))})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-8 w-8 text-blue-500"}),(0,a.jsxs)("div",{className:"mr-4",children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.length}),(0,a.jsx)("p",{className:"text-gray-600",children:"مزايد نشط"})]})]})})}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.Z,{className:"h-8 w-8 text-green-500"}),(0,a.jsxs)("div",{className:"mr-4",children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:Array.isArray(e)?e.reduce((e,t)=>e+t.totalBids,0):0}),(0,a.jsx)("p",{className:"text-gray-600",children:"إجمالي المزايدات"})]})]})})}),(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.Z,{className:"h-8 w-8 text-purple-500"}),(0,a.jsxs)("div",{className:"mr-4",children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:Array.isArray(e)?e.reduce((e,t)=>e+t.points,0):0}),(0,a.jsx)("p",{className:"text-gray-600",children:"إجمالي النقاط"})]})]})})})]}),0===e.length?(0,a.jsx)(n.Zb,{children:(0,a.jsx)(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(d.Z,{className:"h-16 w-16 mx-auto mb-4 text-gray-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"لا توجد بيانات حالياً"}),(0,a.jsx)("p",{className:"text-gray-600",children:"لم يتم العثور على أي مزايدات بعد. ابدأ بالمزايدة لتظهر في لوحة الصدارة!"})]})})}):(0,a.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>{var s;return(0,a.jsx)(n.Zb,{className:"".concat(t<3?"border-2 border-primary/20":""),children:(0,a.jsx)(n.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-12 h-12 flex items-center justify-center",children:Z(e.rank)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0 flex items-center",children:[(0,a.jsxs)(l.qE,{className:"mr-4",children:[(0,a.jsx)(l.F$,{src:e.user.profile.avatarUrl,alt:"Avatar"}),(0,a.jsx)(l.Q5,{children:(0,a.jsx)(j.Z,{className:"h-4 w-4 text-gray-500"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 truncate",children:(null===(s=e.user.profile)||void 0===s?void 0:s.fullName)||e.user.email}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse mt-1",children:[(0,a.jsxs)(i.C,{className:"".concat(w(e.badge)," flex items-center"),children:[y(e.badge),(0,a.jsx)("span",{className:"mr-1",children:e.badge})]}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.points," نقطة"]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-8 space-x-reverse text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-primary",children:e.totalBids}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"مزايدة"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.totalAmount.toLocaleString("ar-SA")}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"ر.س"})]})]})]})})},e._id)})}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"يتم تحديث لوحة الصدارة بشكل دوري. النقاط تُحسب بناءً على عدد المزايدات وقيمتها."})})]})}},16831:function(e,t,s){"use strict";s.d(t,{F$:function(){return l},Q5:function(){return o},qE:function(){return c}});var a=s(57437),r=s(2265),n=s(61146),i=s(94508);let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.fC,{ref:t,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...r})});c.displayName=n.fC.displayName;let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.Ee,{ref:t,className:(0,i.cn)("aspect-square h-full w-full",s),...r})});l.displayName=n.Ee.displayName;let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.NY,{ref:t,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...r})});o.displayName=n.NY.displayName},35974:function(e,t,s){"use strict";s.d(t,{C:function(){return c}});var a=s(57437);s(2265);var r=s(90535),n=s(94508);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:s}),t),...r})}},62869:function(e,t,s){"use strict";s.d(t,{z:function(){return o}});var a=s(57437),r=s(2265),n=s(37053),i=s(90535),c=s(94508);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,t)=>{let{className:s,variant:r,size:i,asChild:o=!1,...d}=e,u=o?n.g7:"button";return(0,a.jsx)(u,{className:(0,c.cn)(l({variant:r,size:i,className:s})),ref:t,...d})});o.displayName="Button"},66070:function(e,t,s){"use strict";s.d(t,{Ol:function(){return c},SZ:function(){return o},Zb:function(){return i},aY:function(){return d},ll:function(){return l}});var a=s(57437),r=s(2265),n=s(94508);let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});i.displayName="Card";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...r})});c.displayName="CardHeader";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});l.displayName="CardTitle";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...r})});o.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...r})});d.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"},24895:function(e,t,s){"use strict";s.d(t,{p:function(){return c}});var a=s(2265);let r={toasts:[]},n=[];function i(e){r=e,n.forEach(t=>t(e))}function c(){let[e,t]=(0,a.useState)(r);return(0,a.useEffect)(()=>(n.push(t),()=>{n=n.filter(e=>e!==t)}),[]),{toast:e=>{let{...t}=e;console.log("Toast called with:",t);let s=Math.random().toString(36).substr(2,9),a={...t,id:s},n={...r,toasts:[...r.toasts,a]};return console.log("Updating toast state with:",n),i(n),setTimeout(()=>{i({...r,toasts:r.toasts.filter(e=>e.id!==s)})},8e3),{id:s,dismiss:()=>{i({...r,toasts:r.toasts.filter(e=>e.id!==s)})}}},toasts:e.toasts}}},29116:function(e,t,s){"use strict";s.d(t,{Sb:function(){return c},Xy:function(){return i},kv:function(){return r},zg:function(){return n}});let a=s(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let r={register:e=>a.post("/auth/register",e),login:e=>a.post("/auth/login",e),logout:()=>a.post("/auth/logout"),verifyEmail:e=>a.post("/auth/verify-email",{token:e}),resendVerification:e=>a.post("/auth/resend-verification",{email:e}),forgotPassword:e=>a.post("/auth/forgot-password",{email:e}),resetPassword:e=>a.post("/auth/reset-password",e)},n={getAll:()=>a.get("/auctions"),getById:e=>a.get("/auctions/".concat(e)),create:e=>a.post("/auctions",e),update:(e,t)=>a.put("/auctions/".concat(e),t),delete:e=>a.delete("/auctions/".concat(e)),placeBid:(e,t)=>a.post("/auctions/".concat(e,"/bid"),{amount:t})},i={getFavorites:e=>a.get("/favorites",{params:e}),addFavorite:e=>a.post("/favorites",e),removeFavorite:(e,t)=>a.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,s)=>a.put("/favorites/".concat(e,"/").concat(t),s),checkFavorite:(e,t)=>a.get("/favorites/check/".concat(e,"/").concat(t))},c={getDashboardStats:()=>a.get("/admin/dashboard"),getPendingAccounts:()=>a.get("/admin/pending-accounts"),approvePendingAccount:e=>a.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>a.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>a.get("/admin/users",{params:e}),getById:e=>a.get("/admin/users/".concat(e)),update:(e,t)=>a.put("/admin/users/".concat(e),t),delete:e=>a.delete("/admin/users/".concat(e)),activate:e=>a.post("/admin/users/".concat(e,"/activate")),deactivate:e=>a.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>a.get("/admin/auctions",{params:e}),getById:e=>a.get("/admin/auctions/".concat(e)),approve:e=>a.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>a.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>a.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>a.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>a.get("/admin/tenders",{params:e}),getById:e=>a.get("/admin/tenders/".concat(e)),approve:e=>a.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>a.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>a.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>a.delete("/admin/tenders/".concat(e))},getTender:e=>a.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>a.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>a.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,s)=>a.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),s),reports:{getFinancialReport:e=>a.get("/admin/reports/financial",{params:e}),getUserReport:e=>a.get("/admin/reports/users",{params:e}),getActivityReport:e=>a.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>a.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>a.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>a.get("/admin/settings"),update:e=>a.put("/admin/settings",e),backup:()=>a.post("/admin/settings/backup"),restore:e=>a.post("/admin/settings/restore/".concat(e))}};t.ZP=a},94508:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var a=s(61994),r=s(53335);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,a.W)(t))}}},function(e){e.O(0,[5554,3464,8765,2971,2117,1744],function(){return e(e.s=67055)}),_N_E=e.O()}]);