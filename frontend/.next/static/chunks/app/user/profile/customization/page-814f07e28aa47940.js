(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4848],{68366:function(e,r,s){Promise.resolve().then(s.bind(s,90167))},90167:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return p}});var a=s(57437),l=s(2265),n=s(66070),t=s(95186),d=s(26815),i=s(62869),c=s(16831),o=s(2014),u=s(94630),f=s(83229),m=s(32489);function p(){let[e,r]=(0,l.useState)({profilePicture:"",fullName:"",phone:"",address:"",bio:""}),[s,p]=(0,l.useState)(!1),[h,x]=(0,l.useState)(e);(0,l.useEffect)(()=>{let e={profilePicture:"",fullName:"<PERSON>",phone:"+1234567890",address:"123 Main St, City, Country",bio:"Lorem ipsum dolor sit amet, consectetur adipiscing elit."};r(e),x(e)},[]);let N=e=>{let{name:r,value:s}=e.target;x({...h,[r]:s})};return(0,a.jsx)(o.default,{allowedRoles:["individual","company","admin","government"],children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("header",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"الملف الشخصي"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"تخصيص معلومات حسابك"})]}),s?(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.z,{onClick:()=>{r(h),p(!1)},children:[(0,a.jsx)(f.Z,{className:"w-4 h-4 mr-2"})," حفظ"]}),(0,a.jsxs)(i.z,{variant:"outline",onClick:()=>{x(e),p(!1)},children:[(0,a.jsx)(m.Z,{className:"w-4 h-4 mr-2"})," إلغاء"]})]}):(0,a.jsxs)(i.z,{onClick:()=>p(!0),children:[(0,a.jsx)(u.Z,{className:"w-4 h-4 mr-2"})," تعديل"]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsx)(n.Ol,{children:(0,a.jsx)(n.ll,{children:"معلومات الملف الشخصي"})}),(0,a.jsxs)(n.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,a.jsxs)(c.qE,{children:[(0,a.jsx)(c.F$,{src:e.profilePicture||"https://via.placeholder.com/150"}),(0,a.jsx)(c.Q5,{children:e.fullName?e.fullName[0]:"A"})]}),s&&(0,a.jsx)(t.I,{id:"profilePicture",name:"profilePicture",placeholder:"رابط صورة الملف الشخصي",value:h.profilePicture,onChange:N,className:"flex-1"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"fullName",children:"الاسم الكامل"}),s?(0,a.jsx)(t.I,{id:"fullName",name:"fullName",value:h.fullName,onChange:N}):(0,a.jsx)("p",{className:"p-2 bg-gray-50 rounded border",children:e.fullName})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"phone",children:"رقم الهاتف"}),s?(0,a.jsx)(t.I,{id:"phone",name:"phone",value:h.phone,onChange:N}):(0,a.jsx)("p",{className:"p-2 bg-gray-50 rounded border",children:e.phone})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"address",children:"العنوان"}),s?(0,a.jsx)(t.I,{id:"address",name:"address",value:h.address,onChange:N}):(0,a.jsx)("p",{className:"p-2 bg-gray-50 rounded border",children:e.address})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d._,{htmlFor:"bio",children:"السيرة الذاتية"}),s?(0,a.jsx)("textarea",{id:"bio",name:"bio",value:h.bio,onChange:N,className:"p-2 w-full h-24 bg-gray-50 rounded border"}):(0,a.jsx)("p",{className:"p-2 bg-gray-50 rounded border space-y-2",children:e.bio})]})]})]})]})]})})}},2014:function(e,r,s){"use strict";s.d(r,{default:function(){return d}});var a=s(57437),l=s(2265),n=s(99376),t=s(63119);function d(e){let{children:r,allowedRoles:s}=e,[d,i]=(0,l.useState)(null),[c,o]=(0,l.useState)(!0),u=(0,n.useRouter)();return((0,l.useEffect)(()=>{let e=localStorage.getItem("token"),r=localStorage.getItem("user");if(!e||!r){u.push("/auth/login");return}let a=JSON.parse(r);if(!s.includes(a.role)){switch(a.role){case"admin":case"super_admin":u.push("/admin/dashboard");break;case"company":u.push("/company/dashboard");break;case"individual":u.push("/user/dashboard");break;case"government":u.push("/government/dashboard");break;default:u.push("/auth/login")}return}if("admin"!==a.role&&"super_admin"!==a.role&&"approved"!==a.status){u.push("/account-status");return}i(a),o(!1)},[u,s]),c)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-muted-foreground",children:"جاري التحميل..."})]})}):d?(0,a.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,a.jsx)(t.Z,{userRole:d.role}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:(0,a.jsx)("div",{className:"p-6",children:r})})]}):null}},16831:function(e,r,s){"use strict";s.d(r,{F$:function(){return i},Q5:function(){return c},qE:function(){return d}});var a=s(57437),l=s(2265),n=s(61146),t=s(94508);let d=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)(n.fC,{ref:r,className:(0,t.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...l})});d.displayName=n.fC.displayName;let i=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)(n.Ee,{ref:r,className:(0,t.cn)("aspect-square h-full w-full",s),...l})});i.displayName=n.Ee.displayName;let c=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)(n.NY,{ref:r,className:(0,t.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...l})});c.displayName=n.NY.displayName},66070:function(e,r,s){"use strict";s.d(r,{Ol:function(){return d},SZ:function(){return c},Zb:function(){return t},aY:function(){return o},ll:function(){return i}});var a=s(57437),l=s(2265),n=s(94508);let t=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...l})});t.displayName="Card";let d=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...l})});d.displayName="CardHeader";let i=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...l})});i.displayName="CardTitle";let c=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",s),...l})});c.displayName="CardDescription";let o=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",s),...l})});o.displayName="CardContent",l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"},95186:function(e,r,s){"use strict";s.d(r,{I:function(){return t}});var a=s(57437),l=s(2265),n=s(94508);let t=l.forwardRef((e,r)=>{let{className:s,type:l,...t}=e;return(0,a.jsx)("input",{type:l,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...t})});t.displayName="Input"},26815:function(e,r,s){"use strict";s.d(r,{_:function(){return c}});var a=s(57437),l=s(2265),n=s(6394),t=s(90535),d=s(94508);let i=(0,t.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)(n.f,{ref:r,className:(0,d.cn)(i(),s),...l})});c.displayName=n.f.displayName}},function(e){e.O(0,[5554,5650,9843,3119,2971,2117,1744],function(){return e(e.s=68366)}),_N_E=e.O()}]);