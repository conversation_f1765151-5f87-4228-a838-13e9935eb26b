(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2616],{9713:function(e,r,t){Promise.resolve().then(t.bind(t,7594))},7594:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return m}});var s=t(57437),n=t(2265),a=t(99376),c=t(27648),i=t(66070),u=t(62869);let o=(0,t(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var l=t(65302),d=t(45131);function f(){let[e,r]=(0,n.useState)("loading"),[t,f]=(0,n.useState)(""),m=(0,a.useSearchParams)(),h=(0,a.useRouter)(),x=m.get("token");return(0,n.useEffect)(()=>{(async()=>{if(!x){r("error"),f("رمز التحقق مفقود");return}try{let e=await fetch("".concat("http://localhost:5000/api","/auth/verify-email?token=").concat(x)),t=await e.json();t.success?(r("success"),f("تم تأكيد البريد الإلكتروني بنجاح! يمكنك الآن تسجيل الدخول.")):(r("error"),f(t.message||"فشل في تأكيد البريد الإلكتروني"))}catch(e){r("error"),f("حدث خطأ أثناء تأكيد البريد الإلكتروني")}})()},[x]),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8 max-w-md",children:(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"text-center",children:[(0,s.jsxs)(i.ll,{className:"flex items-center justify-center gap-2",children:["loading"===e&&(0,s.jsx)(o,{className:"h-6 w-6 animate-spin"}),"success"===e&&(0,s.jsx)(l.Z,{className:"h-6 w-6 text-green-500"}),"error"===e&&(0,s.jsx)(d.Z,{className:"h-6 w-6 text-red-500"}),"تأكيد البريد الإلكتروني"]}),(0,s.jsxs)(i.SZ,{children:["loading"===e&&"جاري تأكيد البريد الإلكتروني...","success"===e&&"تم التأكيد بنجاح","error"===e&&"فشل في التأكيد"]})]}),(0,s.jsxs)(i.aY,{className:"text-center space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:t}),"loading"!==e&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(u.z,{onClick:()=>{"success"===e?h.push("/auth/login"):h.push("/auth/register")},className:"w-full",children:"success"===e?"تسجيل الدخول":"إعادة التسجيل"}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(c.default,{href:"/",className:"text-sm text-muted-foreground hover:text-primary",children:"← العودة إلى الصفحة الرئيسية"})})]})]})]})})}function m(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)(i.Zb,{className:"w-full max-w-md",children:(0,s.jsx)(i.aY,{className:"pt-6",children:(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)(o,{className:"h-8 w-8 animate-spin"})})})})}),children:(0,s.jsx)(f,{})})}},62869:function(e,r,t){"use strict";t.d(r,{z:function(){return o}});var s=t(57437),n=t(2265),a=t(37053),c=t(90535),i=t(94508);let u=(0,c.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,r)=>{let{className:t,variant:n,size:c,asChild:o=!1,...l}=e,d=o?a.g7:"button";return(0,s.jsx)(d,{className:(0,i.cn)(u({variant:n,size:c,className:t})),ref:r,...l})});o.displayName="Button"},66070:function(e,r,t){"use strict";t.d(r,{Ol:function(){return i},SZ:function(){return o},Zb:function(){return c},aY:function(){return l},ll:function(){return u}});var s=t(57437),n=t(2265),a=t(94508);let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});c.displayName="Card";let i=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...n})});i.displayName="CardHeader";let u=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});u.displayName="CardTitle";let o=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",t),...n})});o.displayName="CardDescription";let l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...n})});l.displayName="CardContent",n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"},94508:function(e,r,t){"use strict";t.d(r,{cn:function(){return a}});var s=t(61994),n=t(53335);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.m6)((0,s.W)(r))}},65302:function(e,r,t){"use strict";t.d(r,{Z:function(){return s}});let s=(0,t(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},45131:function(e,r,t){"use strict";t.d(r,{Z:function(){return s}});let s=(0,t(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},99376:function(e,r,t){"use strict";var s=t(35475);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})}},function(e){e.O(0,[5554,5650,2971,2117,1744],function(){return e(e.s=9713)}),_N_E=e.O()}]);