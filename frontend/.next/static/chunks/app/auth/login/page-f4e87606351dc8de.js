(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6716],{11513:function(e,t,s){Promise.resolve().then(s.bind(s,56873))},56873:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return j}});var r=s(57437),a=s(2265),n=s(29501),i=s(13590),o=s(91115),d=s(99376),l=s(27648),c=s(66070),u=s(62869),m=s(95186),f=s(26815),p=s(42208),g=s(22252),h=s(65302),x=s(87769),b=s(29116),v=s(24895);let y=o.z.object({email:o.z.string().email("البريد الإلكتروني غير صحيح"),password:o.z.string().min(6,"كلمة المرور يجب أن تكون 6 أحرف على الأقل")});function j(){let[e,t]=(0,a.useState)(!1),[s,o]=(0,a.useState)(!1),[j,N]=(0,a.useState)(""),[w,k]=(0,a.useState)(""),S=(0,d.useRouter)(),{toast:P}=(0,v.p)(),{register:R,handleSubmit:Z,formState:{errors:I},watch:A}=(0,n.cI)({resolver:(0,i.F)(y)}),C=async e=>{o(!0),N(""),k("");try{let t=(await b.kv.login(e)).data;if(t.success){localStorage.setItem("token",t.data.accessToken),localStorage.setItem("refreshToken",t.data.refreshToken),localStorage.setItem("user",JSON.stringify(t.data.user)),k("تم تسجيل الدخول بنجاح"),P({title:"تم تسجيل الدخول بنجاح",description:"مرحباً بك في منصة المزادات والمناقصات",variant:"default"});let{role:e,status:s}=t.data.user;"admin"===e||"super_admin"===e?S.push("/admin/dashboard"):"approved"===s?S.push("/dashboard"):S.push("/account-status")}else N(t.message||"حدث خطأ أثناء تسجيل الدخول")}catch(r){var t,s;let e="حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.";if(null===(s=r.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message){let t=r.response.data.message;e=t.includes("verify your email")||t.includes("email address before logging")?"يرجى تأكيد بريدك الإلكتروني قبل تسجيل الدخول":t.includes("Invalid credentials")||t.includes("invalid")?"البريد الإلكتروني أو كلمة المرور غير صحيحة":t.includes("suspended")||t.includes("blocked")?"تم تعليق حسابك. يرجى التواصل مع الدعم الفني":t.includes("User not found")?"المستخدم غير موجود. يرجى التحقق من البريد الإلكتروني":t}N(e),P({title:"خطأ في تسجيل الدخول",description:e,variant:"destructive"})}finally{o(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"}),(0,r.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"}),(0,r.jsx)("div",{className:"absolute top-40 left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"})]}),(0,r.jsxs)("div",{className:"w-full max-w-md relative z-10",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)(l.default,{href:"/",className:"inline-flex items-center gap-3 mb-8 group",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:(0,r.jsx)(p.Z,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"منصة المزادات"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"والمناقصات"})]})]}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"مرحباً بعودتك"}),(0,r.jsx)("p",{className:"text-gray-600",children:"ادخل بياناتك للوصول إلى حسابك"})]}),(0,r.jsxs)(c.Zb,{className:"backdrop-blur-sm bg-white/80 border-0 shadow-2xl",children:[(0,r.jsxs)(c.Ol,{className:"text-center pb-4",children:[(0,r.jsx)(c.ll,{className:"text-2xl font-bold text-gray-900",children:"تسجيل الدخول"}),(0,r.jsx)(c.SZ,{className:"text-gray-600",children:"يرجى ملء البيانات التالية للمتابعة"})]}),(0,r.jsxs)(c.aY,{className:"p-8 pt-0",children:[j&&(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl p-4 mb-6 shadow-lg",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3 mt-0.5",children:(0,r.jsx)(g.Z,{className:"h-4 w-4 text-white"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-red-700 font-medium",children:j}),j.includes("تأكيد بريدك الإلكتروني")&&(0,r.jsxs)("div",{className:"mt-3 pt-3 border-t border-red-200",children:[(0,r.jsx)("p",{className:"text-red-600 text-sm mb-2",children:"لم تتلق رسالة التأكيد؟"}),(0,r.jsx)("button",{type:"button",className:"text-sm bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",disabled:s,onClick:async()=>{try{o(!0);let e=A("email");if(!e){P({title:"خطأ",description:"يرجى إدخال البريد الإلكتروني أولاً",variant:"destructive"});return}(await b.kv.resendVerification(e)).data.success&&P({title:"تم إرسال رسالة التأكيد",description:"يرجى التحقق من بريدك الإلكتروني",variant:"default"})}catch(r){var e,t;let s="حدث خطأ أثناء إرسال رسالة التأكيد";if(null===(t=r.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message){let e=r.response.data.message;s=e.includes("already verified")?"البريد الإلكتروني مؤكد بالفعل":e.includes("not found")?"البريد الإلكتروني غير موجود":e}P({title:"خطأ",description:s,variant:"destructive"})}finally{o(!1)}},children:s?"جارٍ الإرسال...":"إعادة إرسال رسالة التأكيد"})]})]})]})}),w&&(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl p-4 mb-6 shadow-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)(h.Z,{className:"h-4 w-4 text-white"})}),(0,r.jsx)("p",{className:"text-green-700 font-medium",children:w})]})}),(0,r.jsxs)("form",{onSubmit:Z(C),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(f._,{htmlFor:"email",className:"text-gray-700 font-medium",children:"البريد الإلكتروني"}),(0,r.jsx)(m.I,{id:"email",type:"email",placeholder:"<EMAIL>",disabled:s,className:"h-12 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-all duration-300",...R("email")}),I.email&&(0,r.jsxs)("p",{className:"text-sm text-red-500 mt-2 flex items-center gap-1",children:[(0,r.jsx)(g.Z,{className:"w-4 h-4"}),I.email.message]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(f._,{htmlFor:"password",className:"text-gray-700 font-medium",children:"كلمة المرور"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.I,{id:"password",type:e?"text":"password",placeholder:"ادخل كلمة المرور",disabled:s,className:"h-12 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-all duration-300 pr-12",...R("password")}),(0,r.jsx)("button",{type:"button",className:"absolute left-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg hover:bg-gray-100 transition-colors duration-200",onClick:()=>t(!e),disabled:s,children:e?(0,r.jsx)(x.Z,{className:"h-5 w-5 text-gray-500"}):(0,r.jsx)(p.Z,{className:"h-5 w-5 text-gray-500"})})]}),I.password&&(0,r.jsxs)("p",{className:"text-sm text-red-500 mt-2 flex items-center gap-1",children:[(0,r.jsx)(g.Z,{className:"w-4 h-4"}),I.password.message]})]}),(0,r.jsx)("div",{className:"flex items-center justify-end",children:(0,r.jsx)(l.default,{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200",children:"نسيت كلمة المرور؟"})}),(0,r.jsx)(u.z,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",disabled:s,children:s?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["ليس لديك حساب؟"," ",(0,r.jsx)(l.default,{href:"/auth/register",className:"text-blue-600 hover:text-blue-800 font-semibold transition-colors duration-200",children:"إنشاء حساب جديد"})]})})]})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsx)(l.default,{href:"/",className:"inline-flex items-center gap-2 text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200 hover:border-blue-300",children:"← العودة إلى الصفحة الرئيسية"})})]})]})}},62869:function(e,t,s){"use strict";s.d(t,{z:function(){return l}});var r=s(57437),a=s(2265),n=s(37053),i=s(90535),o=s(94508);let d=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:s,variant:a,size:i,asChild:l=!1,...c}=e,u=l?n.g7:"button";return(0,r.jsx)(u,{className:(0,o.cn)(d({variant:a,size:i,className:s})),ref:t,...c})});l.displayName="Button"},66070:function(e,t,s){"use strict";s.d(t,{Ol:function(){return o},SZ:function(){return l},Zb:function(){return i},aY:function(){return c},ll:function(){return d}});var r=s(57437),a=s(2265),n=s(94508);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});i.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});o.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});d.displayName="CardTitle";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});l.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});c.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},95186:function(e,t,s){"use strict";s.d(t,{I:function(){return i}});var r=s(57437),a=s(2265),n=s(94508);let i=a.forwardRef((e,t)=>{let{className:s,type:a,...i}=e;return(0,r.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...i})});i.displayName="Input"},26815:function(e,t,s){"use strict";s.d(t,{_:function(){return l}});var r=s(57437),a=s(2265),n=s(6394),i=s(90535),o=s(94508);let d=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.f,{ref:t,className:(0,o.cn)(d(),s),...a})});l.displayName=n.f.displayName},24895:function(e,t,s){"use strict";s.d(t,{p:function(){return o}});var r=s(2265);let a={toasts:[]},n=[];function i(e){a=e,n.forEach(t=>t(e))}function o(){let[e,t]=(0,r.useState)(a);return(0,r.useEffect)(()=>(n.push(t),()=>{n=n.filter(e=>e!==t)}),[]),{toast:e=>{let{...t}=e;console.log("Toast called with:",t);let s=Math.random().toString(36).substr(2,9),r={...t,id:s},n={...a,toasts:[...a.toasts,r]};return console.log("Updating toast state with:",n),i(n),setTimeout(()=>{i({...a,toasts:a.toasts.filter(e=>e.id!==s)})},8e3),{id:s,dismiss:()=>{i({...a,toasts:a.toasts.filter(e=>e.id!==s)})}}},toasts:e.toasts}}},29116:function(e,t,s){"use strict";s.d(t,{Sb:function(){return o},Xy:function(){return i},kv:function(){return a},zg:function(){return n}});let r=s(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let a={register:e=>r.post("/auth/register",e),login:e=>r.post("/auth/login",e),logout:()=>r.post("/auth/logout"),verifyEmail:e=>r.post("/auth/verify-email",{token:e}),resendVerification:e=>r.post("/auth/resend-verification",{email:e}),forgotPassword:e=>r.post("/auth/forgot-password",{email:e}),resetPassword:e=>r.post("/auth/reset-password",e)},n={getAll:()=>r.get("/auctions"),getById:e=>r.get("/auctions/".concat(e)),create:e=>r.post("/auctions",e),update:(e,t)=>r.put("/auctions/".concat(e),t),delete:e=>r.delete("/auctions/".concat(e)),placeBid:(e,t)=>r.post("/auctions/".concat(e,"/bid"),{amount:t})},i={getFavorites:e=>r.get("/favorites",{params:e}),addFavorite:e=>r.post("/favorites",e),removeFavorite:(e,t)=>r.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,s)=>r.put("/favorites/".concat(e,"/").concat(t),s),checkFavorite:(e,t)=>r.get("/favorites/check/".concat(e,"/").concat(t))},o={getDashboardStats:()=>r.get("/admin/dashboard"),getPendingAccounts:()=>r.get("/admin/pending-accounts"),approvePendingAccount:e=>r.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>r.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>r.get("/admin/users",{params:e}),getById:e=>r.get("/admin/users/".concat(e)),update:(e,t)=>r.put("/admin/users/".concat(e),t),delete:e=>r.delete("/admin/users/".concat(e)),activate:e=>r.post("/admin/users/".concat(e,"/activate")),deactivate:e=>r.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>r.get("/admin/auctions",{params:e}),getById:e=>r.get("/admin/auctions/".concat(e)),approve:e=>r.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>r.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>r.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>r.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>r.get("/admin/tenders",{params:e}),getById:e=>r.get("/admin/tenders/".concat(e)),approve:e=>r.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>r.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>r.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>r.delete("/admin/tenders/".concat(e))},getTender:e=>r.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>r.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>r.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,s)=>r.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),s),reports:{getFinancialReport:e=>r.get("/admin/reports/financial",{params:e}),getUserReport:e=>r.get("/admin/reports/users",{params:e}),getActivityReport:e=>r.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>r.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>r.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>r.get("/admin/settings"),update:e=>r.put("/admin/settings",e),backup:()=>r.post("/admin/settings/backup"),restore:e=>r.post("/admin/settings/restore/".concat(e))}};t.ZP=r},94508:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var r=s(61994),a=s(53335);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}},87769:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},42208:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},99376:function(e,t,s){"use strict";var r=s(35475);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},66840:function(e,t,s){"use strict";s.d(t,{WV:function(){return o},jH:function(){return d}});var r=s(2265),a=s(54887),n=s(37053),i=s(57437),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.Z8)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e,o=a?s:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o,{...n,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function d(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}}},function(e){e.O(0,[5554,3464,5650,1984,2971,2117,1744],function(){return e(e.s=11513)}),_N_E=e.O()}]);