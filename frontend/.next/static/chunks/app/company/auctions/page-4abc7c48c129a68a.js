(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6887,4057,2149],{81334:function(e,t,n){Promise.resolve().then(n.bind(n,92775))},92775:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return y}});var r=n(57437),a=n(2265),s=n(66070),i=n(62869),c=n(35974),o=n(73578),l=n(2014),d=n(60285),u=n(95805),h=n(42208),f=n(94630),p=n(18930),m=n(99376),x=n(29116),g=n(24895);function y(){let[e,t]=(0,a.useState)([]),[n,y]=(0,a.useState)(!1),v=(0,m.useRouter)(),{toast:j}=(0,g.p)();(0,a.useEffect)(()=>{b()},[]);let b=async()=>{try{y(!0);let e=await x.zg.getAll();t(e.data||[])}catch(e){console.error("Error loading company auctions:",e),t([{id:"1",title:"سيارة Toyota Camry 2022",status:"active",endDate:"2024-12-15",currentBid:85e3,bidsCount:8,views:124},{id:"2",title:"أثاث مكتبي مستعمل",status:"ended",endDate:"2024-11-25",currentBid:12e3,bidsCount:5,views:89}]),j({title:"فشل في تحميل المزادات",description:"يتم عرض بيانات تجريبية لاختبار الأزرار",variant:"destructive"})}finally{y(!1)}},k=e=>{switch(e){case"active":return(0,r.jsx)(c.C,{variant:"default",children:"نشط"});case"ended":return(0,r.jsx)(c.C,{variant:"destructive",children:"منتهي"});case"draft":return(0,r.jsx)(c.C,{variant:"secondary",children:"مسودة"});default:return(0,r.jsx)(c.C,{children:e})}},N=async n=>{let r=e.find(e=>e.id===n);if(!r){j({title:"خطأ",description:"لم يتم العثور على المزاد",variant:"destructive"});return}if(confirm('هل أنت متأكد من حذف المزاد:\n\n"'.concat(r.title,'"\n\nهذه العملية غير قابلة للتراجع وسيتم حذف جميع المزايدات المرتبطة به.'))){let i=[...e];try{y(!0),t(e=>e.filter(e=>e.id!==n)),await x.zg.delete(n),j({title:"\uD83D\uDDD1️ تم حذف المزاد بنجاح",description:'تم حذف "'.concat(r.title,'" وجميع البيانات المرتبطة به')})}catch(e){var a,s;console.error("Error deleting auction:",e),t(i),j({title:"فشل في حذف المزاد",description:(null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||"يرجى المحاولة مرة أخرى",variant:"destructive"})}finally{y(!1)}}},w=async t=>{try{let n=e.find(e=>e.id===t);if(!n){j({title:"خطأ",description:"لم يتم العثور على المزاد",variant:"destructive"});return}let r="تفاصيل المزاد:\n\n"+"العنوان: ".concat(n.title,"\n")+"الحالة: ".concat(n.status,"\n")+"تاريخ الانتهاء: ".concat(n.endDate,"\n")+"أعلى مزايدة: ".concat((n.currentBid||0).toLocaleString()," ر.س\n")+"عدد المزايدات: ".concat(n.bidsCount||0,"\n")+"عدد المشاهدات: ".concat(n.views||0);alert(r);try{let e=await x.zg.getById(t);console.log("Fresh auction management data:",e.data),j({title:"\uD83D\uDCCA تم عرض بيانات الإدارة",description:"تم تحديث بيانات المزاد من الخادم"})}catch(e){console.log("Could not fetch fresh data, showing cached data"),j({title:"\uD83D\uDCCA عرض بيانات الإدارة",description:"يتم عرض البيانات المحفوظة محلياً"})}}catch(e){var n,r;console.error("Error viewing auction management:",e),j({title:"فشل في عرض بيانات الإدارة",description:(null===(r=e.response)||void 0===r?void 0:null===(n=r.data)||void 0===n?void 0:n.message)||"يرجى المحاولة مرة أخرى",variant:"destructive"})}},Z=t=>{let n=e.find(e=>e.id===t);if(!n){j({title:"خطأ",description:"لم يتم العثور على المزاد",variant:"destructive"});return}let r=prompt('تحرير المزاد: "'.concat(n.title,'"\n\n')+"اختر ما تريد تحريره:\n1 - العنوان\n2 - تاريخ الانتهاء\n3 - الحالة\n\nأدخل رقم الخيار:","1");if(!r)return;let a={},s="";switch(r){case"1":let i=prompt("أدخل العنوان الجديد:",n.title);i&&i.trim()&&(a.title=i.trim(),s="العنوان");break;case"2":let c=prompt("أدخل تاريخ الانتهاء الجديد (YYYY-MM-DD):",n.endDate);c&&c.trim()&&(a.endDate=c.trim(),s="تاريخ الانتهاء");break;case"3":let o=prompt("أدخل الحالة الجديدة (active/draft/ended):",n.status);o&&["active","draft","ended"].includes(o)&&(a.status=o,s="الحالة");break;default:j({title:"خيار غير صالح",description:"يرجى اختيار رقم صحيح",variant:"destructive"});return}if(0===Object.keys(a).length){j({title:"لم يتم التحديث",description:"لم يتم إدخال قيمة صالحة",variant:"destructive"});return}C(t,a,s)},C=async(n,r,a)=>{let s=[...e];try{y(!0),t(e=>e.map(e=>e.id===n?{...e,...r}:e)),await x.zg.update(n,r),j({title:"✏️ تم تحديث المزاد بنجاح",description:"تم تحديث ".concat(a," بنجاح")})}catch(e){var i,c;console.error("Error updating auction:",e),t(s),j({title:"فشل في تحديث المزاد",description:(null===(c=e.response)||void 0===c?void 0:null===(i=c.data)||void 0===i?void 0:i.message)||"يرجى المحاولة مرة أخرى",variant:"destructive"})}finally{y(!1)}};return(0,r.jsx)(l.default,{allowedRoles:["company"],children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("header",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"مزاداتي"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"إدارة المزادات التي أنشأتها شركتك"})]}),(0,r.jsxs)(i.z,{onClick:()=>v.push("/company/create-auction"),children:[(0,r.jsx)(d.Z,{className:"h-4 w-4 ml-2"}),"إنشاء مزاد جديد"]})]}),e.length>0&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)(s.Zb,{children:(0,r.jsxs)(s.Ol,{className:"flex flex-col items-center",children:[(0,r.jsx)(s.ll,{className:"text-lg",children:"إجمالي المزادات"}),(0,r.jsx)(s.SZ,{className:"text-2xl font-bold text-gray-900",children:e.length})]})}),(0,r.jsx)(s.Zb,{children:(0,r.jsxs)(s.Ol,{className:"flex flex-col items-center",children:[(0,r.jsx)(s.ll,{className:"text-lg",children:"المزادات النشطة"}),(0,r.jsx)(s.SZ,{className:"text-2xl font-bold text-green-600",children:e.filter(e=>"active"===e.status).length})]})}),(0,r.jsx)(s.Zb,{children:(0,r.jsxs)(s.Ol,{className:"flex flex-col items-center",children:[(0,r.jsx)(s.ll,{className:"text-lg",children:"إجمالي المزايدات"}),(0,r.jsx)(s.SZ,{className:"text-2xl font-bold text-blue-600",children:e.reduce((e,t)=>e+(t.bidsCount||0),0)})]})}),(0,r.jsx)(s.Zb,{children:(0,r.jsxs)(s.Ol,{className:"flex flex-col items-center",children:[(0,r.jsx)(s.ll,{className:"text-lg",children:"إجمالي المشاهدات"}),(0,r.jsx)(s.SZ,{className:"text-2xl font-bold text-purple-600",children:e.reduce((e,t)=>e+(t.views||0),0)})]})})]}),(0,r.jsxs)(s.Zb,{children:[(0,r.jsxs)(s.Ol,{children:[(0,r.jsx)(s.ll,{children:"قائمة المزادات"}),(0,r.jsx)(s.SZ,{children:e.length>0?"جميع المزادات التي أنشأتها شركتك":"لا توجد مزادات منشورة حالياً"})]}),(0,r.jsx)(s.aY,{children:n?(0,r.jsx)("div",{className:"py-16 text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"جاري تحميل مزاداتك..."})]})}):0===e.length?(0,r.jsx)("div",{className:"py-16 text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center",children:(0,r.jsx)(d.Z,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"لا توجد مزادات بعد"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"ابدأ بإنشاء أول مزاد لشركتك"}),(0,r.jsxs)(i.z,{onClick:()=>v.push("/company/create-auction"),children:[(0,r.jsx)(d.Z,{className:"h-4 w-4 ml-2"}),"إنشاء مزاد جديد"]})]})]})}):(0,r.jsxs)(o.iA,{children:[(0,r.jsx)(o.xD,{children:(0,r.jsxs)(o.SC,{children:[(0,r.jsx)(o.ss,{children:"عنوان المزاد"}),(0,r.jsx)(o.ss,{children:"الحالة"}),(0,r.jsx)(o.ss,{children:"تاريخ الانتهاء"}),(0,r.jsx)(o.ss,{children:"أعلى مزايدة"}),(0,r.jsx)(o.ss,{children:"عدد المزايدات"}),(0,r.jsx)(o.ss,{children:"المشاهدات"}),(0,r.jsx)(o.ss,{children:"الإجراءات"})]})}),(0,r.jsx)(o.RM,{children:e.map(e=>(0,r.jsxs)(o.SC,{children:[(0,r.jsx)(o.pj,{className:"font-medium",children:e.title}),(0,r.jsx)(o.pj,{children:k(e.status)}),(0,r.jsx)(o.pj,{children:e.endDate}),(0,r.jsx)(o.pj,{children:(e.currentBid||0)>0?"".concat(e.currentBid.toLocaleString()," ر.س"):"لا توجد مزايدات"}),(0,r.jsx)(o.pj,{children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(u.Z,{className:"h-4 w-4 text-muted-foreground"}),e.bidsCount||0]})}),(0,r.jsx)(o.pj,{children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(h.Z,{className:"h-4 w-4 text-muted-foreground"}),e.views||0]})}),(0,r.jsx)(o.pj,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.z,{variant:"outline",size:"icon",onClick:t=>{t.preventDefault(),t.stopPropagation(),alert("View button clicked for: "+e.title),console.log("VIEW BUTTON CLICKED for auction:",e.id),w(e.id)},disabled:n,children:(0,r.jsx)(h.Z,{className:"h-4 w-4"})}),(0,r.jsx)(i.z,{variant:"outline",size:"icon",onClick:t=>{t.preventDefault(),t.stopPropagation(),alert("Edit button clicked for: "+e.title),console.log("EDIT BUTTON CLICKED for auction:",e.id),Z(e.id)},disabled:n,children:(0,r.jsx)(f.Z,{className:"h-4 w-4"})}),(0,r.jsx)(i.z,{variant:"destructive",size:"icon",onClick:t=>{t.preventDefault(),t.stopPropagation(),alert("Delete button clicked for: "+e.title),console.log("DELETE BUTTON CLICKED for auction:",e.id),N(e.id)},disabled:n,children:(0,r.jsx)(p.Z,{className:"h-4 w-4"})})]})})]},e.id))})]})})]})]})})}},2014:function(e,t,n){"use strict";n.d(t,{default:function(){return c}});var r=n(57437),a=n(2265),s=n(99376),i=n(63119);function c(e){let{children:t,allowedRoles:n}=e,[c,o]=(0,a.useState)(null),[l,d]=(0,a.useState)(!0),u=(0,s.useRouter)();return((0,a.useEffect)(()=>{let e=localStorage.getItem("token"),t=localStorage.getItem("user");if(!e||!t){u.push("/auth/login");return}let r=JSON.parse(t);if(!n.includes(r.role)){switch(r.role){case"admin":case"super_admin":u.push("/admin/dashboard");break;case"company":u.push("/company/dashboard");break;case"individual":u.push("/user/dashboard");break;case"government":u.push("/government/dashboard");break;default:u.push("/auth/login")}return}if("admin"!==r.role&&"super_admin"!==r.role&&"approved"!==r.status){u.push("/account-status");return}o(r),d(!1)},[u,n]),l)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-muted-foreground",children:"جاري التحميل..."})]})}):c?(0,r.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,r.jsx)(i.Z,{userRole:c.role}),(0,r.jsx)("main",{className:"flex-1 overflow-auto",children:(0,r.jsx)("div",{className:"p-6",children:t})})]}):null}},35974:function(e,t,n){"use strict";n.d(t,{C:function(){return c}});var r=n(57437);n(2265);var a=n(90535),s=n(94508);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:n,...a}=e;return(0,r.jsx)("div",{className:(0,s.cn)(i({variant:n}),t),...a})}},66070:function(e,t,n){"use strict";n.d(t,{Ol:function(){return c},SZ:function(){return l},Zb:function(){return i},aY:function(){return d},ll:function(){return o}});var r=n(57437),a=n(2265),s=n(94508);let i=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...a})});i.displayName="Card";let c=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",n),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",n),...a})});o.displayName="CardTitle";let l=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",n),...a})});l.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",n),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",n),...a})}).displayName="CardFooter"},73578:function(e,t,n){"use strict";n.d(t,{RM:function(){return o},SC:function(){return l},iA:function(){return i},pj:function(){return u},ss:function(){return d},xD:function(){return c}});var r=n(57437),a=n(2265),s=n(94508);let i=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,s.cn)("w-full caption-bottom text-sm",n),...a})})});i.displayName="Table";let c=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("thead",{ref:t,className:(0,s.cn)("[&_tr]:border-b",n),...a})});c.displayName="TableHeader";let o=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,s.cn)("[&_tr:last-child]:border-0",n),...a})});o.displayName="TableBody",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,s.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",n),...a})}).displayName="TableFooter";let l=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("tr",{ref:t,className:(0,s.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",n),...a})});l.displayName="TableRow";let d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("th",{ref:t,className:(0,s.cn)("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",n),...a})});d.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("td",{ref:t,className:(0,s.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",n),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("caption",{ref:t,className:(0,s.cn)("mt-4 text-sm text-muted-foreground",n),...a})}).displayName="TableCaption"},24895:function(e,t,n){"use strict";n.d(t,{p:function(){return c}});var r=n(2265);let a={toasts:[]},s=[];function i(e){a=e,s.forEach(t=>t(e))}function c(){let[e,t]=(0,r.useState)(a);return(0,r.useEffect)(()=>(s.push(t),()=>{s=s.filter(e=>e!==t)}),[]),{toast:e=>{let{...t}=e;console.log("Toast called with:",t);let n=Math.random().toString(36).substr(2,9),r={...t,id:n},s={...a,toasts:[...a.toasts,r]};return console.log("Updating toast state with:",s),i(s),setTimeout(()=>{i({...a,toasts:a.toasts.filter(e=>e.id!==n)})},8e3),{id:n,dismiss:()=>{i({...a,toasts:a.toasts.filter(e=>e.id!==n)})}}},toasts:e.toasts}}},29116:function(e,t,n){"use strict";n.d(t,{Sb:function(){return c},Xy:function(){return i},kv:function(){return a},zg:function(){return s}});let r=n(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let a={register:e=>r.post("/auth/register",e),login:e=>r.post("/auth/login",e),logout:()=>r.post("/auth/logout"),verifyEmail:e=>r.post("/auth/verify-email",{token:e}),resendVerification:e=>r.post("/auth/resend-verification",{email:e}),forgotPassword:e=>r.post("/auth/forgot-password",{email:e}),resetPassword:e=>r.post("/auth/reset-password",e)},s={getAll:()=>r.get("/auctions"),getById:e=>r.get("/auctions/".concat(e)),create:e=>r.post("/auctions",e),update:(e,t)=>r.put("/auctions/".concat(e),t),delete:e=>r.delete("/auctions/".concat(e)),placeBid:(e,t)=>r.post("/auctions/".concat(e,"/bid"),{amount:t})},i={getFavorites:e=>r.get("/favorites",{params:e}),addFavorite:e=>r.post("/favorites",e),removeFavorite:(e,t)=>r.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,n)=>r.put("/favorites/".concat(e,"/").concat(t),n),checkFavorite:(e,t)=>r.get("/favorites/check/".concat(e,"/").concat(t))},c={getDashboardStats:()=>r.get("/admin/dashboard"),getPendingAccounts:()=>r.get("/admin/pending-accounts"),approvePendingAccount:e=>r.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>r.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>r.get("/admin/users",{params:e}),getById:e=>r.get("/admin/users/".concat(e)),update:(e,t)=>r.put("/admin/users/".concat(e),t),delete:e=>r.delete("/admin/users/".concat(e)),activate:e=>r.post("/admin/users/".concat(e,"/activate")),deactivate:e=>r.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>r.get("/admin/auctions",{params:e}),getById:e=>r.get("/admin/auctions/".concat(e)),approve:e=>r.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>r.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>r.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>r.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>r.get("/admin/tenders",{params:e}),getById:e=>r.get("/admin/tenders/".concat(e)),approve:e=>r.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>r.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>r.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>r.delete("/admin/tenders/".concat(e))},getTender:e=>r.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>r.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>r.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,n)=>r.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),n),reports:{getFinancialReport:e=>r.get("/admin/reports/financial",{params:e}),getUserReport:e=>r.get("/admin/reports/users",{params:e}),getActivityReport:e=>r.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>r.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>r.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>r.get("/admin/settings"),update:e=>r.put("/admin/settings",e),backup:()=>r.post("/admin/settings/backup"),restore:e=>r.post("/admin/settings/restore/".concat(e))}};t.ZP=r},42488:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},94766:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},60044:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},92451:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},10407:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},98617:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},42208:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},48736:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},41169:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Gavel",[["path",{d:"m14 13-7.5 7.5c-.83.83-2.17.83-3 0 0 0 0 0 0 0a2.12 2.12 0 0 1 0-3L11 10",key:"c9cbz0"}],["path",{d:"m16 16 6-6",key:"vzrcl6"}],["path",{d:"m8 8 6-6",key:"18bi4p"}],["path",{d:"m9 7 8 8",key:"5jnvq1"}],["path",{d:"m21 11-8-8",key:"z4y7zo"}]])},25466:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},47692:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},94630:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},60285:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("PlusCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},98728:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88906:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},18930:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},70525:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},49474:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},92369:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},95805:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},99376:function(e,t,n){"use strict";var r=n(35475);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})}},function(e){e.O(0,[5554,3464,5650,3119,2971,2117,1744],function(){return e(e.s=81334)}),_N_E=e.O()}]);