(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4968],{38078:function(e,t,r){Promise.resolve().then(r.bind(r,64162))},64162:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return y}});var n=r(57437),s=r(2265),a=r(66070),i=r(62869),c=r(95186),l=r(26815),o=r(35974),d=r(29116),u=r(92369),f=r(60044),m=r(88906),p=r(22252),x=r(65302),h=r(48736),g=r(42208),v=r(45131);function y(){let[e,t]=(0,s.useState)([]),[r,y]=(0,s.useState)(!0),[j,b]=(0,s.useState)(""),[N,w]=(0,s.useState)(null),[k,Z]=(0,s.useState)(""),[C,_]=(0,s.useState)(null);(0,s.useEffect)(()=>{S()},[]);let S=async()=>{try{let e=await d.Sb.users.getAll();if(e.data.success&&e.data.data&&e.data.data.users){let r=e.data.data.users.filter(e=>"pending_email_verification"===e.status||"documents_submitted"===e.status||"under_review"===e.status||"pending"===e.status);t(r)}else b("خطأ في تحميل الحسابات المعلقة")}catch(e){console.error("Pending accounts error:",e),b("حدث خطأ في الاتصال")}finally{y(!1)}},R=async r=>{_(r);try{await d.Sb.users.activate(r),t(e.filter(e=>e._id!==r)),w(null),alert("تم تفعيل الحساب بنجاح")}catch(e){console.error("Error approving account:",e),alert("حدث خطأ في الاتصال")}finally{_(null)}},A=async r=>{if(!k.trim()){alert("يرجى كتابة سبب الرفض");return}_(r);try{await d.Sb.users.deactivate(r),t(e.filter(e=>e._id!==r)),w(null),Z(""),alert("تم رفض الحساب")}catch(e){console.error("Error rejecting account:",e),alert("حدث خطأ في الاتصال")}finally{_(null)}},z=e=>{switch(e){case"individual":return(0,n.jsx)(u.Z,{className:"h-5 w-5 text-purple-500"});case"company":return(0,n.jsx)(f.Z,{className:"h-5 w-5 text-green-500"});case"government":return(0,n.jsx)(m.Z,{className:"h-5 w-5 text-red-500"});default:return(0,n.jsx)(u.Z,{className:"h-5 w-5"})}},M=e=>{switch(e){case"individual":return"فرد";case"company":return"شركة";case"government":return"جهة حكومية";default:return e}},E=e=>{switch(e){case"pending_email_verification":return(0,n.jsx)(o.C,{variant:"secondary",children:"في انتظار تأكيد البريد"});case"documents_submitted":return(0,n.jsx)(o.C,{variant:"secondary",children:"وثائق مقدمة"});case"under_review":return(0,n.jsx)(o.C,{variant:"outline",children:"تحت المراجعة"});case"pending":return(0,n.jsx)(o.C,{variant:"outline",children:"معلق"});case"approved":return(0,n.jsx)(o.C,{variant:"default",children:"موافق عليه"});case"suspended":return(0,n.jsx)(o.C,{variant:"destructive",children:"معلق"});default:return(0,n.jsx)(o.C,{children:e})}};return r?(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("p",{children:"جاري تحميل الحسابات المعلقة..."})}):j?(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(p.Z,{className:"h-5 w-5 text-red-500 mr-2"}),(0,n.jsx)("p",{className:"text-red-700",children:j})]})}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"الحسابات المعلقة"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"مراجعة وموافقة على طلبات تفعيل الحسابات الجديدة"})]}),0===e.length?(0,n.jsx)(a.Zb,{children:(0,n.jsxs)(a.aY,{className:"text-center py-12",children:[(0,n.jsx)(x.Z,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"لا توجد حسابات معلقة"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"جميع طلبات التفعيل تم مراجعتها"})]})}):(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("h2",{className:"text-xl font-semibold",children:["قائمة الحسابات (",e.length,")"]}),e.map(e=>(0,n.jsx)(a.Zb,{className:"cursor-pointer transition-all ".concat((null==N?void 0:N._id)===e._id?"ring-2 ring-primary border-primary":"hover:shadow-md"),onClick:()=>w(e),children:(0,n.jsx)(a.aY,{className:"p-4",children:(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[z(e.role),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium",children:e.profile.fullName||e.profile.companyName||e.profile.governmentEntity}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,n.jsx)(o.C,{variant:"outline",children:M(e.role)}),E(e.status)]})]})]}),(0,n.jsxs)("div",{className:"text-left",children:[(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.submittedAt||e.createdAt).toLocaleDateString("ar-SA")}),(0,n.jsxs)("div",{className:"flex items-center mt-1",children:[(0,n.jsx)(h.Z,{className:"h-4 w-4 text-muted-foreground mr-1"}),(0,n.jsxs)("span",{className:"text-sm",children:[e.documents.length," وثيقة"]})]})]})]})})},e._id))]}),(0,n.jsx)("div",{className:"sticky top-4",children:N?(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{children:[(0,n.jsxs)(a.ll,{className:"flex items-center gap-2",children:[z(N.role),"تفاصيل الحساب"]}),(0,n.jsx)(a.SZ,{children:"مراجعة بيانات ووثائق المستخدم"})]}),(0,n.jsxs)(a.aY,{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium mb-3",children:"المعلومات الأساسية"}),(0,n.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"البريد الإلكتروني:"}),(0,n.jsx)("span",{children:N.email})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"نوع الحساب:"}),(0,n.jsx)("span",{children:M(N.role)})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"الهاتف:"}),(0,n.jsx)("span",{children:N.profile.phone||"غير محدد"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"تاريخ التقديم:"}),(0,n.jsx)("span",{children:new Date(N.submittedAt||N.createdAt).toLocaleDateString("ar-SA")})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("h4",{className:"font-medium mb-3",children:["الوثائق المرفقة (",N.documents.length,")"]}),(0,n.jsx)("div",{className:"space-y-2",children:N.documents.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(h.Z,{className:"h-4 w-4 text-muted-foreground mr-2"}),(0,n.jsx)("span",{className:"text-sm",children:e.name})]}),(0,n.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>window.open(e.url,"_blank"),children:[(0,n.jsx)(g.Z,{className:"h-4 w-4 mr-1"}),"عرض"]})]},t))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l._,{htmlFor:"rejectionReason",children:"سبب الرفض (اختياري)"}),(0,n.jsx)(c.I,{id:"rejectionReason",value:k,onChange:e=>Z(e.target.value),placeholder:"اكتب سبب الرفض إذا كنت تنوي رفض الطلب"})]}),(0,n.jsxs)("div",{className:"flex gap-3",children:[(0,n.jsxs)(i.z,{onClick:()=>R(N._id),disabled:C===N._id,className:"flex-1",children:[(0,n.jsx)(x.Z,{className:"h-4 w-4 mr-1"}),C===N._id?"جاري التفعيل...":"تفعيل الحساب"]}),(0,n.jsxs)(i.z,{variant:"destructive",onClick:()=>A(N._id),disabled:C===N._id,className:"flex-1",children:[(0,n.jsx)(v.Z,{className:"h-4 w-4 mr-1"}),C===N._id?"جاري الرفض...":"رفض الطلب"]})]})]})]}):(0,n.jsx)(a.Zb,{children:(0,n.jsxs)(a.aY,{className:"text-center py-12",children:[(0,n.jsx)(g.Z,{className:"h-16 w-16 text-muted-foreground mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium mb-2",children:"اختر حساباً للمراجعة"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"انقر على أحد الحسابات من القائمة لعرض التفاصيل"})]})})})]})]})}},35974:function(e,t,r){"use strict";r.d(t,{C:function(){return c}});var n=r(57437);r(2265);var s=r(90535),a=r(94508);let i=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:r,...s}=e;return(0,n.jsx)("div",{className:(0,a.cn)(i({variant:r}),t),...s})}},62869:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var n=r(57437),s=r(2265),a=r(37053),i=r(90535),c=r(94508);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:o=!1,...d}=e,u=o?a.g7:"button";return(0,n.jsx)(u,{className:(0,c.cn)(l({variant:s,size:i,className:r})),ref:t,...d})});o.displayName="Button"},66070:function(e,t,r){"use strict";r.d(t,{Ol:function(){return c},SZ:function(){return o},Zb:function(){return i},aY:function(){return d},ll:function(){return l}});var n=r(57437),s=r(2265),a=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...s})});c.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...s})});o.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var n=r(57437),s=r(2265),a=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,type:s,...i}=e;return(0,n.jsx)("input",{type:s,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},26815:function(e,t,r){"use strict";r.d(t,{_:function(){return o}});var n=r(57437),s=r(2265),a=r(6394),i=r(90535),c=r(94508);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.f,{ref:t,className:(0,c.cn)(l(),r),...s})});o.displayName=a.f.displayName},29116:function(e,t,r){"use strict";r.d(t,{Sb:function(){return c},Xy:function(){return i},kv:function(){return s},zg:function(){return a}});let n=r(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let s={register:e=>n.post("/auth/register",e),login:e=>n.post("/auth/login",e),logout:()=>n.post("/auth/logout"),verifyEmail:e=>n.post("/auth/verify-email",{token:e}),resendVerification:e=>n.post("/auth/resend-verification",{email:e}),forgotPassword:e=>n.post("/auth/forgot-password",{email:e}),resetPassword:e=>n.post("/auth/reset-password",e)},a={getAll:()=>n.get("/auctions"),getById:e=>n.get("/auctions/".concat(e)),create:e=>n.post("/auctions",e),update:(e,t)=>n.put("/auctions/".concat(e),t),delete:e=>n.delete("/auctions/".concat(e)),placeBid:(e,t)=>n.post("/auctions/".concat(e,"/bid"),{amount:t})},i={getFavorites:e=>n.get("/favorites",{params:e}),addFavorite:e=>n.post("/favorites",e),removeFavorite:(e,t)=>n.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,r)=>n.put("/favorites/".concat(e,"/").concat(t),r),checkFavorite:(e,t)=>n.get("/favorites/check/".concat(e,"/").concat(t))},c={getDashboardStats:()=>n.get("/admin/dashboard"),getPendingAccounts:()=>n.get("/admin/pending-accounts"),approvePendingAccount:e=>n.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>n.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>n.get("/admin/users",{params:e}),getById:e=>n.get("/admin/users/".concat(e)),update:(e,t)=>n.put("/admin/users/".concat(e),t),delete:e=>n.delete("/admin/users/".concat(e)),activate:e=>n.post("/admin/users/".concat(e,"/activate")),deactivate:e=>n.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>n.get("/admin/auctions",{params:e}),getById:e=>n.get("/admin/auctions/".concat(e)),approve:e=>n.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>n.get("/admin/tenders",{params:e}),getById:e=>n.get("/admin/tenders/".concat(e)),approve:e=>n.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/tenders/".concat(e))},getTender:e=>n.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>n.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>n.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,r)=>n.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),r),reports:{getFinancialReport:e=>n.get("/admin/reports/financial",{params:e}),getUserReport:e=>n.get("/admin/reports/users",{params:e}),getActivityReport:e=>n.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>n.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>n.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>n.get("/admin/settings"),update:e=>n.put("/admin/settings",e),backup:()=>n.post("/admin/settings/backup"),restore:e=>n.post("/admin/settings/restore/".concat(e))}};t.ZP=n},94508:function(e,t,r){"use strict";r.d(t,{cn:function(){return a}});var n=r(61994),s=r(53335);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,n.W)(t))}},22252:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},60044:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},65302:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},42208:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},48736:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},88906:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},92369:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},45131:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},98575:function(e,t,r){"use strict";r.d(t,{F:function(){return a},e:function(){return i}});var n=r(2265);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},6394:function(e,t,r){"use strict";r.d(t,{f:function(){return c}});var n=r(2265),s=r(66840),a=r(57437),i=n.forwardRef((e,t)=>(0,a.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var c=i},66840:function(e,t,r){"use strict";r.d(t,{WV:function(){return c},jH:function(){return l}});var n=r(2265),s=r(54887),a=r(37053),i=r(57437),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.Z8)(`Primitive.${t}`),s=n.forwardRef((e,n)=>{let{asChild:s,...a}=e,c=s?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(c,{...a,ref:n})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function l(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},37053:function(e,t,r){"use strict";r.d(t,{Z8:function(){return i},g7:function(){return c}});var n=r(2265),s=r(98575),a=r(57437);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e,i;let c=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,l=function(e,t){let r={...t};for(let n in t){let s=e[n],a=t[n];/^on[A-Z]/.test(n)?s&&a?r[n]=(...e)=>{let t=a(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...a}:"className"===n&&(r[n]=[s,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(l.ref=t?(0,s.F)(t,c):c),n.cloneElement(r,l)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...i}=e,c=n.Children.toArray(s),l=c.find(o);if(l){let e=l.props.children,s=c.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,a.jsx)(t,{...i,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var c=i("Slot"),l=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},90535:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});var n=r(61994);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.W,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:c}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==c?void 0:c[e];if(null===t)return null;let a=s(t)||s(n);return i[e][a]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,l,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...c,...o}[t]):({...c,...o})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[5554,3464,2971,2117,1744],function(){return e(e.s=38078)}),_N_E=e.O()}]);