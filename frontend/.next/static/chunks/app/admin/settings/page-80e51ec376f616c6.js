(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6140],{44612:function(e,t,n){Promise.resolve().then(n.bind(n,8307))},8307:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return i}});var s=n(57437),a=n(2265);function i(){let[e,t]=(0,a.useState)({siteName:"BridgePoint",siteDescription:"Professional Auction & Tender Platform",contactEmail:"<EMAIL>",supportEmail:"<EMAIL>",maintenanceMode:!1,registrationEnabled:!0,auctionDuration:7,tenderDuration:14,commissionRate:5,maxBidAmount:1e6,maxTenderAmount:5e6,emailNotifications:!0,smsNotifications:!1,autoApproveAccounts:!1,requireVerification:!0,allowGuestBidding:!1,timezone:"UTC",currency:"USD",language:"en"}),[n,i]=(0,a.useState)(!1),[r,l]=(0,a.useState)(""),o=async t=>{t.preventDefault(),i(!0),l("");try{(await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?l("Settings updated successfully!"):l("Failed to update settings")}catch(e){l("Error updating settings")}finally{i(!1)}},c=(e,n)=>{t(t=>({...t,[e]:n}))};return(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"System Settings"}),r&&(0,s.jsx)("div",{className:"mb-4 p-4 rounded-md ".concat(r.includes("successfully")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"),children:r}),(0,s.jsxs)("form",{onSubmit:o,className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"General Settings"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Site Name"}),(0,s.jsx)("input",{type:"text",value:e.siteName,onChange:e=>c("siteName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Contact Email"}),(0,s.jsx)("input",{type:"email",value:e.contactEmail,onChange:e=>c("contactEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Site Description"}),(0,s.jsx)("textarea",{value:e.siteDescription,onChange:e=>c("siteDescription",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Platform Settings"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Auction Duration (days)"}),(0,s.jsx)("input",{type:"number",value:e.auctionDuration,onChange:e=>c("auctionDuration",parseInt(e.target.value)),min:"1",max:"30",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Tender Duration (days)"}),(0,s.jsx)("input",{type:"number",value:e.tenderDuration,onChange:e=>c("tenderDuration",parseInt(e.target.value)),min:"1",max:"60",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Commission Rate (%)"}),(0,s.jsx)("input",{type:"number",value:e.commissionRate,onChange:e=>c("commissionRate",parseFloat(e.target.value)),min:"0",max:"20",step:"0.1",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Currency"}),(0,s.jsxs)("select",{value:e.currency,onChange:e=>c("currency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"USD",children:"USD - US Dollar"}),(0,s.jsx)("option",{value:"EUR",children:"EUR - Euro"}),(0,s.jsx)("option",{value:"GBP",children:"GBP - British Pound"}),(0,s.jsx)("option",{value:"CAD",children:"CAD - Canadian Dollar"}),(0,s.jsx)("option",{value:"AUD",children:"AUD - Australian Dollar"})]})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"System Controls"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Maintenance Mode"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Temporarily disable site access for maintenance"})]}),(0,s.jsx)("button",{type:"button",onClick:()=>c("maintenanceMode",!e.maintenanceMode),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(e.maintenanceMode?"bg-red-600":"bg-gray-200"),children:(0,s.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(e.maintenanceMode?"translate-x-6":"translate-x-1")})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Registration Enabled"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Allow new user registrations"})]}),(0,s.jsx)("button",{type:"button",onClick:()=>c("registrationEnabled",!e.registrationEnabled),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(e.registrationEnabled?"bg-green-600":"bg-gray-200"),children:(0,s.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(e.registrationEnabled?"translate-x-6":"translate-x-1")})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Auto-approve Accounts"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Automatically approve new user accounts"})]}),(0,s.jsx)("button",{type:"button",onClick:()=>c("autoApproveAccounts",!e.autoApproveAccounts),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(e.autoApproveAccounts?"bg-green-600":"bg-gray-200"),children:(0,s.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(e.autoApproveAccounts?"translate-x-6":"translate-x-1")})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Email Notifications"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Send email notifications to users"})]}),(0,s.jsx)("button",{type:"button",onClick:()=>c("emailNotifications",!e.emailNotifications),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(e.emailNotifications?"bg-green-600":"bg-gray-200"),children:(0,s.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(e.emailNotifications?"translate-x-6":"translate-x-1")})})]})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{type:"submit",disabled:n,className:"bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50",children:n?"Saving...":"Save Settings"})})]})]})})}}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=44612)}),_N_E=e.O()}]);