(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9674],{73580:function(e,t,n){Promise.resolve().then(n.bind(n,33400))},33400:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return h}});var r=n(57437),s=n(2265),a=n(66070),i=n(62869),o=n(35974),c=n(16831),d=n(29116),u=n(14438),l=n(89345),f=n(13041),p=n(83774),m=n(31047),v=n(39763);let g=(0,v.Z)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]),x=(0,v.Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);function h(){let[e,t]=(0,s.useState)([]),[n,v]=(0,s.useState)(!0),[h,y]=(0,s.useState)("all");(0,s.useEffect)(()=>{j()},[]);let j=async()=>{try{v(!0);let e=await d.Sb.users.getAll();e.data.success&&e.data.data&&e.data.data.users?t(e.data.data.users):(console.error("Unexpected API response structure:",e.data),t([]))}catch(e){console.error("Error fetching users:",e),u.Am.error("Failed to fetch users"),t([])}finally{v(!1)}},b=async(n,r)=>{try{"approved"===r?await d.Sb.users.activate(n):await d.Sb.users.deactivate(n),t(e.map(e=>e._id===n?{...e,status:r}:e)),u.Am.success("User ".concat("approved"===r?"activated":"suspended"," successfully"))}catch(e){console.error("Error updating user status:",e),u.Am.error("Failed to update user status")}},N=e.filter(e=>"all"===h||e.status===h);return n?(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"User Management"}),(0,r.jsx)("div",{className:"grid gap-4",children:[...Array(6)].map((e,t)=>(0,r.jsx)(a.Zb,{className:"animate-pulse",children:(0,r.jsx)(a.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gray-300 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-300 rounded w-1/3"})]})]})})},t))})]}):(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"User Management"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.z,{variant:"all"===h?"default":"outline",onClick:()=>y("all"),children:"All Users"}),(0,r.jsx)(i.z,{variant:"approved"===h?"default":"outline",onClick:()=>y("approved"),children:"Approved"}),(0,r.jsx)(i.z,{variant:"pending"===h?"default":"outline",onClick:()=>y("pending"),children:"Pending"}),(0,r.jsx)(i.z,{variant:"suspended"===h?"default":"outline",onClick:()=>y("suspended"),children:"Suspended"})]})]}),(0,r.jsx)("div",{className:"grid gap-4",children:N.map(e=>{var t,n,s,d,u,v,h,y,j;return(0,r.jsxs)(a.Zb,{children:[(0,r.jsx)(a.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(c.qE,{children:[(0,r.jsx)(c.F$,{src:"https://api.dicebear.com/7.x/initials/svg?seed=".concat((null===(t=e.profile)||void 0===t?void 0:t.fullName)||(null===(n=e.profile)||void 0===n?void 0:n.companyName)||e.email)}),(0,r.jsx)(c.Q5,{children:((null===(s=e.profile)||void 0===s?void 0:s.fullName)||(null===(d=e.profile)||void 0===d?void 0:d.companyName)||e.email).charAt(0).toUpperCase()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(a.ll,{className:"text-lg",children:(null===(u=e.profile)||void 0===u?void 0:u.fullName)||(null===(v=e.profile)||void 0===v?void 0:v.companyName)||(null===(h=e.profile)||void 0===h?void 0:h.governmentEntity)||"No Name"}),(0,r.jsxs)(a.SZ,{className:"flex items-center gap-4",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(l.Z,{className:"w-4 h-4"}),e.email]}),(null===(y=e.profile)||void 0===y?void 0:y.phone)&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(f.Z,{className:"w-4 h-4"}),e.profile.phone]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.C,{variant:"approved"===e.status?"default":"pending"===e.status||"pending_email_verification"===e.status||"documents_submitted"===e.status||"under_review"===e.status?"secondary":"destructive",children:"pending_email_verification"===e.status?"Email Pending":"documents_submitted"===e.status?"Docs Submitted":"under_review"===e.status?"Under Review":e.status}),(0,r.jsx)(o.C,{variant:"outline",children:e.role})]})]})}),(0,r.jsx)(a.aY,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(null===(j=e.profile)||void 0===j?void 0:j.address)&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(p.Z,{className:"w-4 h-4"}),e.profile.address]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(m.Z,{className:"w-4 h-4"}),"Joined ",new Date(e.createdAt).toLocaleDateString()]}),e.lastLogin&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:["Last login: ",new Date(e.lastLogin).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:["approved"===e.status&&(0,r.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>b(e._id,"suspended"),children:[(0,r.jsx)(g,{className:"w-4 h-4 mr-2"}),"Suspend"]}),"suspended"===e.status&&(0,r.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>b(e._id,"approved"),children:[(0,r.jsx)(x,{className:"w-4 h-4 mr-2"}),"Activate"]}),("pending"===e.status||"pending_email_verification"===e.status||"documents_submitted"===e.status||"under_review"===e.status)&&(0,r.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>b(e._id,"approved"),children:[(0,r.jsx)(x,{className:"w-4 h-4 mr-2"}),"Approve"]})]})]})})]},e._id)})}),0===N.length&&(0,r.jsx)(a.Zb,{children:(0,r.jsx)(a.aY,{className:"p-8 text-center",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No users found for the selected filter."})})})]})}},16831:function(e,t,n){"use strict";n.d(t,{F$:function(){return c},Q5:function(){return d},qE:function(){return o}});var r=n(57437),s=n(2265),a=n(61146),i=n(94508);let o=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.fC,{ref:t,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",n),...s})});o.displayName=a.fC.displayName;let c=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.Ee,{ref:t,className:(0,i.cn)("aspect-square h-full w-full",n),...s})});c.displayName=a.Ee.displayName;let d=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.NY,{ref:t,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",n),...s})});d.displayName=a.NY.displayName},35974:function(e,t,n){"use strict";n.d(t,{C:function(){return o}});var r=n(57437);n(2265);var s=n(90535),a=n(94508);let i=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:n,...s}=e;return(0,r.jsx)("div",{className:(0,a.cn)(i({variant:n}),t),...s})}},62869:function(e,t,n){"use strict";n.d(t,{z:function(){return d}});var r=n(57437),s=n(2265),a=n(37053),i=n(90535),o=n(94508);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:n,variant:s,size:i,asChild:d=!1,...u}=e,l=d?a.g7:"button";return(0,r.jsx)(l,{className:(0,o.cn)(c({variant:s,size:i,className:n})),ref:t,...u})});d.displayName="Button"},66070:function(e,t,n){"use strict";n.d(t,{Ol:function(){return o},SZ:function(){return d},Zb:function(){return i},aY:function(){return u},ll:function(){return c}});var r=n(57437),s=n(2265),a=n(94508);let i=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...s})});i.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",n),...s})});o.displayName="CardHeader";let c=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",n),...s})});c.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",n),...s})});d.displayName="CardDescription";let u=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",n),...s})});u.displayName="CardContent",s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",n),...s})}).displayName="CardFooter"},29116:function(e,t,n){"use strict";n.d(t,{Sb:function(){return o},Xy:function(){return i},kv:function(){return s},zg:function(){return a}});let r=n(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let s={register:e=>r.post("/auth/register",e),login:e=>r.post("/auth/login",e),logout:()=>r.post("/auth/logout"),verifyEmail:e=>r.post("/auth/verify-email",{token:e}),resendVerification:e=>r.post("/auth/resend-verification",{email:e}),forgotPassword:e=>r.post("/auth/forgot-password",{email:e}),resetPassword:e=>r.post("/auth/reset-password",e)},a={getAll:()=>r.get("/auctions"),getById:e=>r.get("/auctions/".concat(e)),create:e=>r.post("/auctions",e),update:(e,t)=>r.put("/auctions/".concat(e),t),delete:e=>r.delete("/auctions/".concat(e)),placeBid:(e,t)=>r.post("/auctions/".concat(e,"/bid"),{amount:t})},i={getFavorites:e=>r.get("/favorites",{params:e}),addFavorite:e=>r.post("/favorites",e),removeFavorite:(e,t)=>r.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,n)=>r.put("/favorites/".concat(e,"/").concat(t),n),checkFavorite:(e,t)=>r.get("/favorites/check/".concat(e,"/").concat(t))},o={getDashboardStats:()=>r.get("/admin/dashboard"),getPendingAccounts:()=>r.get("/admin/pending-accounts"),approvePendingAccount:e=>r.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>r.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>r.get("/admin/users",{params:e}),getById:e=>r.get("/admin/users/".concat(e)),update:(e,t)=>r.put("/admin/users/".concat(e),t),delete:e=>r.delete("/admin/users/".concat(e)),activate:e=>r.post("/admin/users/".concat(e,"/activate")),deactivate:e=>r.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>r.get("/admin/auctions",{params:e}),getById:e=>r.get("/admin/auctions/".concat(e)),approve:e=>r.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>r.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>r.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>r.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>r.get("/admin/tenders",{params:e}),getById:e=>r.get("/admin/tenders/".concat(e)),approve:e=>r.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>r.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>r.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>r.delete("/admin/tenders/".concat(e))},getTender:e=>r.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>r.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>r.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,n)=>r.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),n),reports:{getFinancialReport:e=>r.get("/admin/reports/financial",{params:e}),getUserReport:e=>r.get("/admin/reports/users",{params:e}),getActivityReport:e=>r.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>r.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>r.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>r.get("/admin/settings"),update:e=>r.put("/admin/settings",e),backup:()=>r.post("/admin/settings/backup"),restore:e=>r.post("/admin/settings/restore/".concat(e))}};t.ZP=r},94508:function(e,t,n){"use strict";n.d(t,{cn:function(){return a}});var r=n(61994),s=n(53335);function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.m6)((0,r.W)(t))}},31047:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},89345:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},83774:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13041:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},24369:function(e,t,n){"use strict";var r=n(2265),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,i=r.useEffect,o=r.useLayoutEffect,c=r.useDebugValue;function d(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!s(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=a({inst:{value:n,getSnapshot:t}}),s=r[0].inst,u=r[1];return o(function(){s.value=n,s.getSnapshot=t,d(s)&&u({inst:s})},[e,n,t]),i(function(){return d(s)&&u({inst:s}),e(function(){d(s)&&u({inst:s})})},[e]),c(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},82558:function(e,t,n){"use strict";e.exports=n(24369)},61146:function(e,t,n){"use strict";n.d(t,{NY:function(){return k},Ee:function(){return w},fC:function(){return N}});var r=n(2265),s=n(73966),a=n(26606),i=n(61188),o=n(66840),c=n(82558);function d(){return()=>{}}var u=n(57437),l="Avatar",[f,p]=(0,s.b)(l),[m,v]=f(l),g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...s}=e,[a,i]=r.useState("idle");return(0,u.jsx)(m,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:(0,u.jsx)(o.WV.span,{...s,ref:t})})});g.displayName=l;var x="AvatarImage",h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:s,onLoadingStatusChange:l=()=>{},...f}=e,p=v(x,n),m=function(e,t){let{referrerPolicy:n,crossOrigin:s}=t,a=(0,c.useSyncExternalStore)(d,()=>!0,()=>!1),o=r.useRef(null),u=a?(o.current||(o.current=new window.Image),o.current):null,[l,f]=r.useState(()=>b(u,e));return(0,i.b)(()=>{f(b(u,e))},[u,e]),(0,i.b)(()=>{let e=e=>()=>{f(e)};if(!u)return;let t=e("loaded"),r=e("error");return u.addEventListener("load",t),u.addEventListener("error",r),n&&(u.referrerPolicy=n),"string"==typeof s&&(u.crossOrigin=s),()=>{u.removeEventListener("load",t),u.removeEventListener("error",r)}},[u,s,n]),l}(s,f),g=(0,a.W)(e=>{l(e),p.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==m&&g(m)},[m,g]),"loaded"===m?(0,u.jsx)(o.WV.img,{...f,ref:t,src:s}):null});h.displayName=x;var y="AvatarFallback",j=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:s,...a}=e,i=v(y,n),[c,d]=r.useState(void 0===s);return r.useEffect(()=>{if(void 0!==s){let e=window.setTimeout(()=>d(!0),s);return()=>window.clearTimeout(e)}},[s]),c&&"loaded"!==i.imageLoadingStatus?(0,u.jsx)(o.WV.span,{...a,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}j.displayName=y;var N=g,w=h,k=j},73966:function(e,t,n){"use strict";n.d(t,{b:function(){return a}});var r=n(2265),s=n(57437);function a(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let s=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:s}}),[n,s])}};return a.scopeName=e,[function(t,a){let i=r.createContext(a),o=n.length;n=[...n,a];let c=t=>{let{scope:n,children:a,...c}=t,d=n?.[e]?.[o]||i,u=r.useMemo(()=>c,Object.values(c));return(0,s.jsx)(d.Provider,{value:u,children:a})};return c.displayName=t+"Provider",[c,function(n,s){let c=s?.[e]?.[o]||i,d=r.useContext(c);if(d)return d;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=n.reduce((t,{useScope:n,scopeName:r})=>{let s=n(e)[`__scope${r}`];return{...t,...s}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}(a,...t)]}},66840:function(e,t,n){"use strict";n.d(t,{WV:function(){return o},jH:function(){return c}});var r=n(2265),s=n(54887),a=n(37053),i=n(57437),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,a.Z8)(`Primitive.${t}`),s=r.forwardRef((e,r)=>{let{asChild:s,...a}=e,o=s?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o,{...a,ref:r})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function c(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},26606:function(e,t,n){"use strict";n.d(t,{W:function(){return s}});var r=n(2265);function s(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},61188:function(e,t,n){"use strict";n.d(t,{b:function(){return s}});var r=n(2265),s=globalThis?.document?r.useLayoutEffect:()=>{}}},function(e){e.O(0,[5554,3464,4172,2971,2117,1744],function(){return e(e.s=73580)}),_N_E=e.O()}]);