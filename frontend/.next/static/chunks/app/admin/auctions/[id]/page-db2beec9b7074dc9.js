(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4362],{27487:function(e,t,s){Promise.resolve().then(s.bind(s,73295))},73295:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return g}});var n=s(57437),r=s(2265),a=s(99376),i=s(62869),c=s(66070),o=s(35974),d=s(55156),l=s(32660),u=s(94630),m=s(18930),p=s(29116),f=s(14438);function g(){let{id:e}=(0,a.useParams)(),t=(0,a.useRouter)(),[s,g]=(0,r.useState)(null),[x,h]=(0,r.useState)(!0),[v,j]=(0,r.useState)(!1);(0,r.useEffect)(()=>{b()},[e]);let b=async()=>{try{h(!0);let t=await p.Sb.auctions.getById(e);g(t.data)}catch(e){console.error("Error fetching auction:",e),f.Am.error("Failed to load auction details")}finally{h(!1)}},y=async()=>{if(confirm("Are you sure you want to delete this auction? This action cannot be undone."))try{j(!0),await p.Sb.auctions.delete(e),f.Am.success("Auction deleted successfully"),t.push("/admin/auctions")}catch(e){console.error("Error deleting auction:",e),f.Am.error("Failed to delete auction")}finally{j(!1)}};return x?(0,n.jsx)("div",{className:"container mx-auto p-6",children:(0,n.jsxs)("div",{className:"animate-pulse",children:[(0,n.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]})]})}):s?(0,n.jsxs)("div",{className:"container mx-auto p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsxs)(i.z,{variant:"ghost",onClick:()=>t.push("/admin/auctions"),children:[(0,n.jsx)(l.Z,{className:"mr-2 h-4 w-4"}),"Back to Auctions"]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(i.z,{variant:"outline",onClick:()=>t.push("/admin/auctions/".concat(e,"/edit")),children:[(0,n.jsx)(u.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,n.jsxs)(i.z,{variant:"destructive",onClick:y,disabled:v,children:[(0,n.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),v?"Deleting...":"Delete"]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,n.jsx)("div",{className:"lg:col-span-2",children:(0,n.jsxs)(c.Zb,{children:[(0,n.jsx)(c.Ol,{children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(c.ll,{className:"text-2xl",children:s.title}),(0,n.jsx)(o.C,{className:(e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"completed":return"bg-blue-100 text-blue-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(s.status),children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]})}),(0,n.jsx)(c.aY,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"Description"}),(0,n.jsx)("p",{className:"text-gray-700",children:s.description})]}),(0,n.jsx)(d.Z,{}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"Starting Price"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:["$",s.startingPrice.toLocaleString()]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"Current Price"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-blue-600",children:["$",s.currentPrice.toLocaleString()]})]})]}),(0,n.jsx)(d.Z,{}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"Start Date"}),(0,n.jsx)("p",{children:new Date(s.startDate).toLocaleDateString()})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"End Date"}),(0,n.jsx)("p",{children:new Date(s.endDate).toLocaleDateString()})]})]}),(0,n.jsx)(d.Z,{}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"Category"}),(0,n.jsx)(o.C,{variant:"secondary",children:s.category})]})]})})]})}),(0,n.jsxs)("div",{children:[(0,n.jsxs)(c.Zb,{children:[(0,n.jsx)(c.Ol,{children:(0,n.jsx)(c.ll,{children:"Recent Bids"})}),(0,n.jsx)(c.aY,{children:s.bids&&s.bids.length>0?(0,n.jsx)("div",{className:"space-y-3",children:s.bids.slice(0,5).map(e=>(0,n.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("p",{className:"font-semibold",children:["$",e.amount.toLocaleString()]}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.bidder})]}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.timestamp).toLocaleDateString()})]},e.id))}):(0,n.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No bids yet"})})]}),(0,n.jsxs)(c.Zb,{className:"mt-6",children:[(0,n.jsx)(c.Ol,{children:(0,n.jsx)(c.ll,{children:"Metadata"})}),(0,n.jsx)(c.aY,{children:(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Created"}),(0,n.jsx)("p",{className:"text-sm",children:new Date(s.createdAt).toLocaleString()})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Last Updated"}),(0,n.jsx)("p",{className:"text-sm",children:new Date(s.updatedAt).toLocaleString()})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Auction ID"}),(0,n.jsx)("p",{className:"text-sm font-mono",children:s.id})]})]})})]})]})]})]}):(0,n.jsx)("div",{className:"container mx-auto p-6",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Auction Not Found"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"The auction you're looking for doesn't exist."}),(0,n.jsxs)(i.z,{onClick:()=>t.push("/admin/auctions"),children:[(0,n.jsx)(l.Z,{className:"mr-2 h-4 w-4"}),"Back to Auctions"]})]})})}},35974:function(e,t,s){"use strict";s.d(t,{C:function(){return c}});var n=s(57437);s(2265);var r=s(90535),a=s(94508);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,...r}=e;return(0,n.jsx)("div",{className:(0,a.cn)(i({variant:s}),t),...r})}},62869:function(e,t,s){"use strict";s.d(t,{z:function(){return d}});var n=s(57437),r=s(2265),a=s(37053),i=s(90535),c=s(94508);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:s,variant:r,size:i,asChild:d=!1,...l}=e,u=d?a.g7:"button";return(0,n.jsx)(u,{className:(0,c.cn)(o({variant:r,size:i,className:s})),ref:t,...l})});d.displayName="Button"},66070:function(e,t,s){"use strict";s.d(t,{Ol:function(){return c},SZ:function(){return d},Zb:function(){return i},aY:function(){return l},ll:function(){return o}});var n=s(57437),r=s(2265),a=s(94508);let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});i.displayName="Card";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...r})});c.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",s),...r})});d.displayName="CardDescription";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",s),...r})});l.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"},29116:function(e,t,s){"use strict";s.d(t,{Sb:function(){return c},Xy:function(){return i},kv:function(){return r},zg:function(){return a}});let n=s(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let r={register:e=>n.post("/auth/register",e),login:e=>n.post("/auth/login",e),logout:()=>n.post("/auth/logout"),verifyEmail:e=>n.post("/auth/verify-email",{token:e}),resendVerification:e=>n.post("/auth/resend-verification",{email:e}),forgotPassword:e=>n.post("/auth/forgot-password",{email:e}),resetPassword:e=>n.post("/auth/reset-password",e)},a={getAll:()=>n.get("/auctions"),getById:e=>n.get("/auctions/".concat(e)),create:e=>n.post("/auctions",e),update:(e,t)=>n.put("/auctions/".concat(e),t),delete:e=>n.delete("/auctions/".concat(e)),placeBid:(e,t)=>n.post("/auctions/".concat(e,"/bid"),{amount:t})},i={getFavorites:e=>n.get("/favorites",{params:e}),addFavorite:e=>n.post("/favorites",e),removeFavorite:(e,t)=>n.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,s)=>n.put("/favorites/".concat(e,"/").concat(t),s),checkFavorite:(e,t)=>n.get("/favorites/check/".concat(e,"/").concat(t))},c={getDashboardStats:()=>n.get("/admin/dashboard"),getPendingAccounts:()=>n.get("/admin/pending-accounts"),approvePendingAccount:e=>n.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>n.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>n.get("/admin/users",{params:e}),getById:e=>n.get("/admin/users/".concat(e)),update:(e,t)=>n.put("/admin/users/".concat(e),t),delete:e=>n.delete("/admin/users/".concat(e)),activate:e=>n.post("/admin/users/".concat(e,"/activate")),deactivate:e=>n.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>n.get("/admin/auctions",{params:e}),getById:e=>n.get("/admin/auctions/".concat(e)),approve:e=>n.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>n.get("/admin/tenders",{params:e}),getById:e=>n.get("/admin/tenders/".concat(e)),approve:e=>n.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/tenders/".concat(e))},getTender:e=>n.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>n.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>n.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,s)=>n.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),s),reports:{getFinancialReport:e=>n.get("/admin/reports/financial",{params:e}),getUserReport:e=>n.get("/admin/reports/users",{params:e}),getActivityReport:e=>n.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>n.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>n.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>n.get("/admin/settings"),update:e=>n.put("/admin/settings",e),backup:()=>n.post("/admin/settings/backup"),restore:e=>n.post("/admin/settings/restore/".concat(e))}};t.ZP=n},94508:function(e,t,s){"use strict";s.d(t,{cn:function(){return a}});var n=s(61994),r=s(53335);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,n.W)(t))}},32660:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},94630:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},18930:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},99376:function(e,t,s){"use strict";var n=s(35475);s.o(n,"useParams")&&s.d(t,{useParams:function(){return n.useParams}}),s.o(n,"usePathname")&&s.d(t,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(t,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(t,{useSearchParams:function(){return n.useSearchParams}})},66840:function(e,t,s){"use strict";s.d(t,{WV:function(){return c},jH:function(){return o}});var n=s(2265),r=s(54887),a=s(37053),i=s(57437),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,a.Z8)(`Primitive.${t}`),r=n.forwardRef((e,n)=>{let{asChild:r,...a}=e,c=r?s:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(c,{...a,ref:n})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function o(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},55156:function(e,t,s){"use strict";s.d(t,{Z:function(){return o},f:function(){return d}});var n=s(2265),r=s(66840),a=s(57437),i="horizontal",c=["horizontal","vertical"],o=n.forwardRef((e,t)=>{let{decorative:s,orientation:n=i,...o}=e,d=c.includes(n)?n:i;return(0,a.jsx)(r.WV.div,{"data-orientation":d,...s?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...o,ref:t})});o.displayName="Separator";var d=o}},function(e){e.O(0,[5554,3464,4172,2971,2117,1744],function(){return e(e.s=27487)}),_N_E=e.O()}]);