(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1465],{92090:function(e,t,r){Promise.resolve().then(r.bind(r,3310))},3310:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return h}});var a=r(57437),n=r(2265),s=r(99376),i=r(62869),o=r(95186),d=r(26815),c=r(76818),l=r(66070),u=r(53647),p=r(32660),f=r(83229),m=r(29116),g=r(14438);function h(){let{id:e}=(0,s.useParams)(),t=(0,s.useRouter)(),[r,h]=(0,n.useState)(!0),[x,v]=(0,n.useState)(!1),[j,y]=(0,n.useState)({title:"",description:"",startingPrice:0,startDate:"",endDate:"",status:"pending",category:""});(0,n.useEffect)(()=>{b()},[e]);let b=async()=>{try{h(!0);let t=(await m.Sb.auctions.getById(e)).data;y({title:t.title,description:t.description,startingPrice:t.startingPrice,startDate:new Date(t.startDate).toISOString().split("T")[0],endDate:new Date(t.endDate).toISOString().split("T")[0],status:t.status,category:t.category})}catch(e){console.error("Error fetching auction:",e),g.Am.error("Failed to load auction data")}finally{h(!1)}},w=async r=>{if(r.preventDefault(),!j.title||!j.description||!j.category){g.Am.error("Please fill in all required fields");return}if(j.startingPrice<=0){g.Am.error("Starting price must be greater than 0");return}if(new Date(j.startDate)>=new Date(j.endDate)){g.Am.error("End date must be after start date");return}try{v(!0),await m.zg.update(e,j),g.Am.success("Auction updated successfully"),t.push("/admin/auctions/".concat(e))}catch(e){console.error("Error updating auction:",e),g.Am.error("Failed to update auction")}finally{v(!1)}},N=(e,t)=>{y(r=>({...r,[e]:t}))};return r?(0,a.jsx)("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]})]})}):(0,a.jsxs)("div",{className:"container mx-auto p-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,a.jsxs)(i.z,{variant:"ghost",onClick:()=>t.push("/admin/auctions/".concat(e)),children:[(0,a.jsx)(p.Z,{className:"mr-2 h-4 w-4"}),"Back to Auction Details"]})}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsx)(l.ll,{children:"Edit Auction"})}),(0,a.jsx)(l.aY,{children:(0,a.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d._,{htmlFor:"title",children:"Title *"}),(0,a.jsx)(o.I,{id:"title",value:j.title,onChange:e=>N("title",e.target.value),placeholder:"Enter auction title",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d._,{htmlFor:"category",children:"Category *"}),(0,a.jsxs)(u.Ph,{value:j.category,onValueChange:e=>N("category",e),children:[(0,a.jsx)(u.i4,{children:(0,a.jsx)(u.ki,{placeholder:"Select category"})}),(0,a.jsxs)(u.Bw,{children:[(0,a.jsx)(u.Ql,{value:"electronics",children:"Electronics"}),(0,a.jsx)(u.Ql,{value:"art",children:"Art"}),(0,a.jsx)(u.Ql,{value:"collectibles",children:"Collectibles"}),(0,a.jsx)(u.Ql,{value:"vehicles",children:"Vehicles"}),(0,a.jsx)(u.Ql,{value:"real-estate",children:"Real Estate"}),(0,a.jsx)(u.Ql,{value:"jewelry",children:"Jewelry"}),(0,a.jsx)(u.Ql,{value:"antiques",children:"Antiques"}),(0,a.jsx)(u.Ql,{value:"other",children:"Other"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d._,{htmlFor:"startingPrice",children:"Starting Price *"}),(0,a.jsx)(o.I,{id:"startingPrice",type:"number",min:"0",step:"0.01",value:j.startingPrice,onChange:e=>N("startingPrice",parseFloat(e.target.value)||0),placeholder:"0.00",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d._,{htmlFor:"status",children:"Status"}),(0,a.jsxs)(u.Ph,{value:j.status,onValueChange:e=>N("status",e),children:[(0,a.jsx)(u.i4,{children:(0,a.jsx)(u.ki,{})}),(0,a.jsxs)(u.Bw,{children:[(0,a.jsx)(u.Ql,{value:"pending",children:"Pending"}),(0,a.jsx)(u.Ql,{value:"active",children:"Active"}),(0,a.jsx)(u.Ql,{value:"completed",children:"Completed"}),(0,a.jsx)(u.Ql,{value:"cancelled",children:"Cancelled"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d._,{htmlFor:"startDate",children:"Start Date *"}),(0,a.jsx)(o.I,{id:"startDate",type:"date",value:j.startDate,onChange:e=>N("startDate",e.target.value),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d._,{htmlFor:"endDate",children:"End Date *"}),(0,a.jsx)(o.I,{id:"endDate",type:"date",value:j.endDate,onChange:e=>N("endDate",e.target.value),required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d._,{htmlFor:"description",children:"Description *"}),(0,a.jsx)(c.g,{id:"description",value:j.description,onChange:e=>N("description",e.target.value),placeholder:"Enter detailed description of the auction item",rows:4,required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)(i.z,{type:"submit",disabled:x,className:"flex items-center gap-2",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4"}),x?"Saving...":"Save Changes"]}),(0,a.jsx)(i.z,{type:"button",variant:"outline",onClick:()=>t.push("/admin/auctions/".concat(e)),children:"Cancel"})]})]})})]})]})}},62869:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var a=r(57437),n=r(2265),s=r(37053),i=r(90535),o=r(94508);let d=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:r,variant:n,size:i,asChild:c=!1,...l}=e,u=c?s.g7:"button";return(0,a.jsx)(u,{className:(0,o.cn)(d({variant:n,size:i,className:r})),ref:t,...l})});c.displayName="Button"},66070:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return l},ll:function(){return d}});var a=r(57437),n=r(2265),s=r(94508);let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});i.displayName="Card";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...n})});o.displayName="CardHeader";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});d.displayName="CardTitle";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...n})});c.displayName="CardDescription";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...n})});l.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var a=r(57437),n=r(2265),s=r(94508);let i=n.forwardRef((e,t)=>{let{className:r,type:n,...i}=e;return(0,a.jsx)("input",{type:n,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},26815:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var a=r(57437),n=r(2265),s=r(6394),i=r(90535),o=r(94508);let d=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.f,{ref:t,className:(0,o.cn)(d(),r),...n})});c.displayName=s.f.displayName},53647:function(e,t,r){"use strict";r.d(t,{Bw:function(){return g},Ph:function(){return l},Ql:function(){return h},i4:function(){return p},ki:function(){return u}});var a=r(57437),n=r(2265),s=r(57864),i=r(40875),o=r(22135),d=r(30401),c=r(94508);let l=s.fC;s.ZA;let u=s.B4,p=n.forwardRef((e,t)=>{let{className:r,children:n,...o}=e;return(0,a.jsxs)(s.xz,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...o,children:[n,(0,a.jsx)(s.JO,{asChild:!0,children:(0,a.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});p.displayName=s.xz.displayName;let f=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,a.jsx)(o.Z,{className:"h-4 w-4"})})});f.displayName=s.u_.displayName;let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});m.displayName=s.$G.displayName;let g=n.forwardRef((e,t)=>{let{className:r,children:n,position:i="popper",...o}=e;return(0,a.jsx)(s.h_,{children:(0,a.jsxs)(s.VY,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:i,...o,children:[(0,a.jsx)(f,{}),(0,a.jsx)(s.l_,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,a.jsx)(m,{})]})})});g.displayName=s.VY.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...n})}).displayName=s.__.displayName;let h=n.forwardRef((e,t)=>{let{className:r,children:n,...i}=e;return(0,a.jsxs)(s.ck,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(s.wU,{children:(0,a.jsx)(d.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(s.eT,{children:n})]})});h.displayName=s.ck.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",r),...n})}).displayName=s.Z0.displayName},76818:function(e,t,r){"use strict";r.d(t,{g:function(){return i}});var a=r(57437),n=r(2265),s=r(94508);let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});i.displayName="Textarea"},29116:function(e,t,r){"use strict";r.d(t,{Sb:function(){return o},Xy:function(){return i},kv:function(){return n},zg:function(){return s}});let a=r(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let n={register:e=>a.post("/auth/register",e),login:e=>a.post("/auth/login",e),logout:()=>a.post("/auth/logout"),verifyEmail:e=>a.post("/auth/verify-email",{token:e}),resendVerification:e=>a.post("/auth/resend-verification",{email:e}),forgotPassword:e=>a.post("/auth/forgot-password",{email:e}),resetPassword:e=>a.post("/auth/reset-password",e)},s={getAll:()=>a.get("/auctions"),getById:e=>a.get("/auctions/".concat(e)),create:e=>a.post("/auctions",e),update:(e,t)=>a.put("/auctions/".concat(e),t),delete:e=>a.delete("/auctions/".concat(e)),placeBid:(e,t)=>a.post("/auctions/".concat(e,"/bid"),{amount:t})},i={getFavorites:e=>a.get("/favorites",{params:e}),addFavorite:e=>a.post("/favorites",e),removeFavorite:(e,t)=>a.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,r)=>a.put("/favorites/".concat(e,"/").concat(t),r),checkFavorite:(e,t)=>a.get("/favorites/check/".concat(e,"/").concat(t))},o={getDashboardStats:()=>a.get("/admin/dashboard"),getPendingAccounts:()=>a.get("/admin/pending-accounts"),approvePendingAccount:e=>a.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>a.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>a.get("/admin/users",{params:e}),getById:e=>a.get("/admin/users/".concat(e)),update:(e,t)=>a.put("/admin/users/".concat(e),t),delete:e=>a.delete("/admin/users/".concat(e)),activate:e=>a.post("/admin/users/".concat(e,"/activate")),deactivate:e=>a.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>a.get("/admin/auctions",{params:e}),getById:e=>a.get("/admin/auctions/".concat(e)),approve:e=>a.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>a.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>a.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>a.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>a.get("/admin/tenders",{params:e}),getById:e=>a.get("/admin/tenders/".concat(e)),approve:e=>a.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>a.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>a.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>a.delete("/admin/tenders/".concat(e))},getTender:e=>a.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>a.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>a.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,r)=>a.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),r),reports:{getFinancialReport:e=>a.get("/admin/reports/financial",{params:e}),getUserReport:e=>a.get("/admin/reports/users",{params:e}),getActivityReport:e=>a.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>a.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>a.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>a.get("/admin/settings"),update:e=>a.put("/admin/settings",e),backup:()=>a.post("/admin/settings/backup"),restore:e=>a.post("/admin/settings/restore/".concat(e))}};t.ZP=a},94508:function(e,t,r){"use strict";r.d(t,{cn:function(){return s}});var a=r(61994),n=r(53335);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,a.W)(t))}},32660:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},83229:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},99376:function(e,t,r){"use strict";var a=r(35475);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},6394:function(e,t,r){"use strict";r.d(t,{f:function(){return o}});var a=r(2265),n=r(66840),s=r(57437),i=a.forwardRef((e,t)=>(0,s.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i}},function(e){e.O(0,[5554,3464,6052,4863,4172,2971,2117,1744],function(){return e(e.s=92090)}),_N_E=e.O()}]);