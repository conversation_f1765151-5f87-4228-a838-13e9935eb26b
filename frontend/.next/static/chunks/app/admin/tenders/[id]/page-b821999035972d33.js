(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5305],{44060:function(e,t,s){Promise.resolve().then(s.bind(s,43838))},43838:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return j}});var n=s(57437),r=s(2265),a=s(99376),i=s(66070),c=s(62869),o=s(35974),l=s(24895),d=s(29116),u=s(32660),m=s(95252),p=s(31047),f=s(91723),h=s(92369),g=s(60044),x=s(48736),v=s(65302),y=s(45131);function j(){let e=(0,a.useParams)(),t=(0,a.useRouter)(),{toast:s}=(0,l.p)(),j=e.id,[b,N]=(0,r.useState)(null),[k,w]=(0,r.useState)(!0),[Z,S]=(0,r.useState)([]),[C,P]=(0,r.useState)(!1);(0,r.useEffect)(()=>{j&&R()},[j]);let R=async()=>{try{w(!0);let e=await d.ZP.get("/admin/tenders/".concat(j));e.data.success&&N(e.data.data)}catch(e){console.error("Error fetching tender:",e),s({title:"خطأ في التحميل",description:"فشل في تحميل تفاصيل المناقصة",variant:"destructive"})}finally{w(!1)}},z=async e=>{try{await adminAPI.updateTenderStatus(j,{status:e}),N(t=>t?{...t,status:e}:null),s.success("Tender ".concat(e," successfully"))}catch(e){console.error("Error updating tender status:",e),s.error("Failed to update tender status")}},A=async(e,t)=>{try{await adminAPI.updateTenderSubmissionStatus(j,e,{status:t}),S(s=>s.map(s=>s.id===e?{...s,status:t}:s)),s.success("Submission ".concat(t," successfully"))}catch(e){console.error("Error updating submission status:",e),s.error("Failed to update submission status")}};return k?(0,n.jsx)("div",{className:"p-6",children:(0,n.jsxs)("div",{className:"animate-pulse",children:[(0,n.jsx)("div",{className:"h-8 bg-gray-300 rounded w-1/3 mb-4"}),(0,n.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/2 mb-8"}),(0,n.jsxs)("div",{className:"grid gap-4",children:[(0,n.jsx)("div",{className:"h-32 bg-gray-300 rounded"}),(0,n.jsx)("div",{className:"h-32 bg-gray-300 rounded"})]})]})}):b?(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,n.jsxs)(c.z,{variant:"ghost",onClick:()=>t.back(),children:[(0,n.jsx)(u.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,n.jsx)("h1",{className:"text-2xl font-bold",children:"Tender Details"})]}),(0,n.jsxs)("div",{className:"grid gap-6",children:[(0,n.jsxs)(i.Zb,{children:[(0,n.jsx)(i.Ol,{children:(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(i.ll,{className:"text-xl",children:b.title}),(0,n.jsxs)(i.SZ,{className:"mt-2",children:[b.department," • ",b.category]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(o.C,{variant:"active"===b.status?"default":"closed"===b.status?"secondary":"cancelled"===b.status?"destructive":"outline",children:b.status}),"active"===b.status&&(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(c.z,{variant:"outline",size:"sm",onClick:()=>z("closed"),children:"Close Tender"}),(0,n.jsx)(c.z,{variant:"destructive",size:"sm",onClick:()=>z("cancelled"),children:"Cancel"})]})]})]})}),(0,n.jsx)(i.aY,{children:(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"Description"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:b.description}),(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"Requirements"}),(0,n.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:b.requirements.map((e,t)=>(0,n.jsxs)("li",{className:"flex items-start gap-2",children:[(0,n.jsx)("span",{className:"text-blue-500",children:"•"}),e]},t))})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(m.Z,{className:"w-4 h-4 text-green-500"}),(0,n.jsxs)("span",{className:"font-semibold",children:["Budget: $",b.budget.toLocaleString()]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(p.Z,{className:"w-4 h-4 text-blue-500"}),(0,n.jsxs)("span",{children:["Start: ",new Date(b.startDate).toLocaleDateString()]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(f.Z,{className:"w-4 h-4 text-orange-500"}),(0,n.jsxs)("span",{children:["End: ",new Date(b.endDate).toLocaleDateString()]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(h.Z,{className:"w-4 h-4 text-purple-500"}),(0,n.jsxs)("span",{children:["Created by: ",b.createdBy.name]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(g.Z,{className:"w-4 h-4 text-gray-500"}),(0,n.jsxs)("span",{children:["Department: ",b.createdBy.department]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(x.Z,{className:"w-4 h-4 text-blue-500"}),(0,n.jsxs)("span",{children:["Submissions: ",b.submissionCount]})]})]})]})})]}),(0,n.jsxs)(i.Zb,{children:[(0,n.jsxs)(i.Ol,{children:[(0,n.jsxs)(i.ll,{children:["Submissions (",Z.length,")"]}),(0,n.jsx)(i.SZ,{children:"Companies that have submitted proposals for this tender"})]}),(0,n.jsx)(i.aY,{children:C?(0,n.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>(0,n.jsxs)("div",{className:"animate-pulse border rounded-lg p-4",children:[(0,n.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/3 mb-2"}),(0,n.jsx)("div",{className:"h-3 bg-gray-300 rounded w-1/2"})]},t))}):0===Z.length?(0,n.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No submissions yet"}):(0,n.jsx)("div",{className:"space-y-4",children:Z.map(e=>(0,n.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold",children:e.companyName}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.contactEmail}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.contactPhone})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("p",{className:"font-semibold text-lg text-green-600",children:["$",e.proposedAmount.toLocaleString()]}),(0,n.jsx)(o.C,{variant:"approved"===e.status?"default":"rejected"===e.status?"destructive":"secondary",children:e.status})]})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,n.jsx)(p.Z,{className:"w-4 h-4"}),"Submitted: ",new Date(e.submittedAt).toLocaleDateString()]}),"pending"===e.status&&(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(c.z,{variant:"outline",size:"sm",onClick:()=>A(e.id,"approved"),children:[(0,n.jsx)(v.Z,{className:"w-4 h-4 mr-2"}),"Approve"]}),(0,n.jsxs)(c.z,{variant:"outline",size:"sm",onClick:()=>A(e.id,"rejected"),children:[(0,n.jsx)(y.Z,{className:"w-4 h-4 mr-2"}),"Reject"]})]})]})]},e.id))})})]})]})]}):(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsx)("div",{className:"flex items-center gap-2 mb-4",children:(0,n.jsxs)(c.z,{variant:"ghost",onClick:()=>t.back(),children:[(0,n.jsx)(u.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,n.jsx)(i.Zb,{children:(0,n.jsx)(i.aY,{className:"p-8 text-center",children:(0,n.jsx)("p",{className:"text-gray-500",children:"Tender not found"})})})]})}},35974:function(e,t,s){"use strict";s.d(t,{C:function(){return c}});var n=s(57437);s(2265);var r=s(90535),a=s(94508);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,...r}=e;return(0,n.jsx)("div",{className:(0,a.cn)(i({variant:s}),t),...r})}},62869:function(e,t,s){"use strict";s.d(t,{z:function(){return l}});var n=s(57437),r=s(2265),a=s(37053),i=s(90535),c=s(94508);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=r.forwardRef((e,t)=>{let{className:s,variant:r,size:i,asChild:l=!1,...d}=e,u=l?a.g7:"button";return(0,n.jsx)(u,{className:(0,c.cn)(o({variant:r,size:i,className:s})),ref:t,...d})});l.displayName="Button"},66070:function(e,t,s){"use strict";s.d(t,{Ol:function(){return c},SZ:function(){return l},Zb:function(){return i},aY:function(){return d},ll:function(){return o}});var n=s(57437),r=s(2265),a=s(94508);let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});i.displayName="Card";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...r})});c.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",s),...r})});l.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",s),...r})});d.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"},24895:function(e,t,s){"use strict";s.d(t,{p:function(){return c}});var n=s(2265);let r={toasts:[]},a=[];function i(e){r=e,a.forEach(t=>t(e))}function c(){let[e,t]=(0,n.useState)(r);return(0,n.useEffect)(()=>(a.push(t),()=>{a=a.filter(e=>e!==t)}),[]),{toast:e=>{let{...t}=e;console.log("Toast called with:",t);let s=Math.random().toString(36).substr(2,9),n={...t,id:s},a={...r,toasts:[...r.toasts,n]};return console.log("Updating toast state with:",a),i(a),setTimeout(()=>{i({...r,toasts:r.toasts.filter(e=>e.id!==s)})},8e3),{id:s,dismiss:()=>{i({...r,toasts:r.toasts.filter(e=>e.id!==s)})}}},toasts:e.toasts}}},29116:function(e,t,s){"use strict";s.d(t,{Sb:function(){return c},Xy:function(){return i},kv:function(){return r},zg:function(){return a}});let n=s(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let r={register:e=>n.post("/auth/register",e),login:e=>n.post("/auth/login",e),logout:()=>n.post("/auth/logout"),verifyEmail:e=>n.post("/auth/verify-email",{token:e}),resendVerification:e=>n.post("/auth/resend-verification",{email:e}),forgotPassword:e=>n.post("/auth/forgot-password",{email:e}),resetPassword:e=>n.post("/auth/reset-password",e)},a={getAll:()=>n.get("/auctions"),getById:e=>n.get("/auctions/".concat(e)),create:e=>n.post("/auctions",e),update:(e,t)=>n.put("/auctions/".concat(e),t),delete:e=>n.delete("/auctions/".concat(e)),placeBid:(e,t)=>n.post("/auctions/".concat(e,"/bid"),{amount:t})},i={getFavorites:e=>n.get("/favorites",{params:e}),addFavorite:e=>n.post("/favorites",e),removeFavorite:(e,t)=>n.delete("/favorites/".concat(e,"/").concat(t)),updateFavorite:(e,t,s)=>n.put("/favorites/".concat(e,"/").concat(t),s),checkFavorite:(e,t)=>n.get("/favorites/check/".concat(e,"/").concat(t))},c={getDashboardStats:()=>n.get("/admin/dashboard"),getPendingAccounts:()=>n.get("/admin/pending-accounts"),approvePendingAccount:e=>n.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,t)=>n.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:t}),users:{getAll:e=>n.get("/admin/users",{params:e}),getById:e=>n.get("/admin/users/".concat(e)),update:(e,t)=>n.put("/admin/users/".concat(e),t),delete:e=>n.delete("/admin/users/".concat(e)),activate:e=>n.post("/admin/users/".concat(e,"/activate")),deactivate:e=>n.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>n.get("/admin/auctions",{params:e}),getById:e=>n.get("/admin/auctions/".concat(e)),approve:e=>n.post("/admin/auctions/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/auctions/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/auctions/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>n.get("/admin/tenders",{params:e}),getById:e=>n.get("/admin/tenders/".concat(e)),approve:e=>n.post("/admin/tenders/".concat(e,"/approve")),reject:(e,t)=>n.post("/admin/tenders/".concat(e,"/reject"),{reason:t}),suspend:(e,t)=>n.post("/admin/tenders/".concat(e,"/suspend"),{reason:t}),delete:e=>n.delete("/admin/tenders/".concat(e))},getTender:e=>n.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,t)=>n.get("/admin/tenders/".concat(e,"/submissions"),{params:t}),updateTenderStatus:(e,t)=>n.put("/admin/tenders/".concat(e,"/status"),t),updateTenderSubmissionStatus:(e,t,s)=>n.put("/admin/tenders/".concat(e,"/submissions/").concat(t,"/status"),s),reports:{getFinancialReport:e=>n.get("/admin/reports/financial",{params:e}),getUserReport:e=>n.get("/admin/reports/users",{params:e}),getActivityReport:e=>n.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>n.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>n.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>n.get("/admin/settings"),update:e=>n.put("/admin/settings",e),backup:()=>n.post("/admin/settings/backup"),restore:e=>n.post("/admin/settings/restore/".concat(e))}};t.ZP=n},94508:function(e,t,s){"use strict";s.d(t,{cn:function(){return a}});var n=s(61994),r=s(53335);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,n.W)(t))}},32660:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},60044:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},31047:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},65302:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},91723:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},95252:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},48736:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},92369:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},45131:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},99376:function(e,t,s){"use strict";var n=s(35475);s.o(n,"useParams")&&s.d(t,{useParams:function(){return n.useParams}}),s.o(n,"usePathname")&&s.d(t,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(t,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(t,{useSearchParams:function(){return n.useSearchParams}})},98575:function(e,t,s){"use strict";s.d(t,{F:function(){return a},e:function(){return i}});var n=s(2265);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let s=!1,n=e.map(e=>{let n=r(e,t);return s||"function"!=typeof n||(s=!0),n});if(s)return()=>{for(let t=0;t<n.length;t++){let s=n[t];"function"==typeof s?s():r(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},37053:function(e,t,s){"use strict";s.d(t,{Z8:function(){return i},g7:function(){return c}});var n=s(2265),r=s(98575),a=s(57437);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:s,...a}=e;if(n.isValidElement(s)){let e,i;let c=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?s.ref:(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?s.props.ref:s.props.ref||s.ref,o=function(e,t){let s={...t};for(let n in t){let r=e[n],a=t[n];/^on[A-Z]/.test(n)?r&&a?s[n]=(...e)=>{let t=a(...e);return r(...e),t}:r&&(s[n]=r):"style"===n?s[n]={...r,...a}:"className"===n&&(s[n]=[r,a].filter(Boolean).join(" "))}return{...e,...s}}(a,s.props);return s.type!==n.Fragment&&(o.ref=t?(0,r.F)(t,c):c),n.cloneElement(s,o)}return n.Children.count(s)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=n.forwardRef((e,s)=>{let{children:r,...i}=e,c=n.Children.toArray(r),o=c.find(l);if(o){let e=o.props.children,r=c.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:s,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,a.jsx)(t,{...i,ref:s,children:r})});return s.displayName=`${e}.Slot`,s}var c=i("Slot"),o=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},90535:function(e,t,s){"use strict";s.d(t,{j:function(){return i}});var n=s(61994);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.W,i=(e,t)=>s=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:i,defaultVariants:c}=t,o=Object.keys(i).map(e=>{let t=null==s?void 0:s[e],n=null==c?void 0:c[e];if(null===t)return null;let a=r(t)||r(n);return i[e][a]}),l=s&&Object.entries(s).reduce((e,t)=>{let[s,n]=t;return void 0===n||(e[s]=n),e},{});return a(e,o,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:s,className:n,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...c,...l}[t]):({...c,...l})[t]===s})?[...e,s,n]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}}},function(e){e.O(0,[5554,3464,2971,2117,1744],function(){return e(e.s=44060)}),_N_E=e.O()}]);