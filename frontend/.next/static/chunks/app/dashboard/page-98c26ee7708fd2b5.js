(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7702],{30130:function(e,r,s){Promise.resolve().then(s.bind(s,71720))},71720:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return t}});var n=s(57437),u=s(2265),a=s(99376);function t(){let e=(0,a.useRouter)();return(0,u.useEffect)(()=>{let r=localStorage.getItem("user");if(!r){e.push("/auth/login");return}try{let{role:s,status:n}=JSON.parse(r);"admin"===s||"super_admin"===s?e.push("/admin/dashboard"):"government"===s?e.push("/government/dashboard"):"company"===s?e.push("/company/dashboard"):e.push("/user/dashboard")}catch(r){console.error("Error parsing user data:",r),e.push("/auth/login")}},[e]),(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"}),(0,n.jsx)("p",{children:"جاري التحويل..."})]})})}},99376:function(e,r,s){"use strict";var n=s(35475);s.o(n,"useParams")&&s.d(r,{useParams:function(){return n.useParams}}),s.o(n,"usePathname")&&s.d(r,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(r,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(r,{useSearchParams:function(){return n.useSearchParams}})}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=30130)}),_N_E=e.O()}]);