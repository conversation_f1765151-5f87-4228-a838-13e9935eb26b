(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6938],{89548:function(e,s,t){Promise.resolve().then(t.bind(t,80421))},80421:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return R}});var a=t(57437),i=t(2265),n=t(66070),r=t(62869),l=t(95186),c=t(26815),d=t(50721),o=t(94508);let u=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(d.fC,{className:(0,o.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...i,ref:s,children:(0,a.jsx)(d.bU,{className:(0,o.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});u.displayName=d.fC.displayName;var m=t(53647),h=t(12339),x=t(6512),f=t(24895),p=t(2014),j=t(29116),g=t(82431),v=t(83229),y=t(94766),N=t(88906),w=t(12805),b=t(66337),k=t(87769),C=t(42208),S=t(63639),Z=t(18930);function R(){let[e,s]=(0,i.useState)(null),[t,d]=(0,i.useState)(!0),[o,R]=(0,i.useState)(!1),[_,P]=(0,i.useState)(null),[F,E]=(0,i.useState)(""),[Q,B]=(0,i.useState)(""),[z,U]=(0,i.useState)(""),[V,A]=(0,i.useState)(!1),{toast:I}=(0,f.p)();(0,i.useEffect)(()=>{let e=localStorage.getItem("user");e&&P(JSON.parse(e)),T()},[]);let T=async()=>{try{d(!0);let e=await j.ZP.get("/users/settings");e.data.success&&s(e.data.data)}catch(e){console.error("Error loading settings:",e),s({notifications:{email:!0,sms:!1,push:!0,newBids:!0,auctionEnding:!0,applicationUpdates:!0,paymentReminders:!0,marketingEmails:!1},privacy:{profileVisibility:"public",showEmail:!1,showPhone:!1,showBidHistory:!0},preferences:{language:"ar",currency:"SAR",timezone:"Asia/Riyadh",theme:"light",itemsPerPage:10},security:{twoFactorEnabled:!1,loginNotifications:!0,sessionTimeout:30}})}finally{d(!1)}},O=async()=>{try{R(!0),(await j.ZP.patch("/users/settings",e)).data.success&&I({title:"تم الحفظ",description:"تم حفظ الإعدادات بنجاح"})}catch(e){I({title:"خطأ في الحفظ",description:"حدث خطأ في حفظ الإعدادات",variant:"destructive"})}finally{R(!1)}},Y=async()=>{if(Q!==z){I({title:"خطأ",description:"كلمات المرور الجديدة غير متطابقة",variant:"destructive"});return}if(Q.length<8){I({title:"خطأ",description:"كلمة المرور يجب أن تكون 8 أحرف على الأقل",variant:"destructive"});return}try{(await j.ZP.patch("/users/change-password",{currentPassword:F,newPassword:Q})).data.success&&(I({title:"تم التحديث",description:"تم تغيير كلمة المرور بنجاح"}),E(""),B(""),U(""))}catch(t){var e,s;I({title:"خطأ",description:(null===(s=t.response)||void 0===s?void 0:null===(e=s.data)||void 0===e?void 0:e.message)||"حدث خطأ في تغيير كلمة المرور",variant:"destructive"})}},D=(t,a,i)=>{e&&s({...e,[t]:{...e[t],[a]:i}})};return t?(0,a.jsx)(p.default,{allowedRoles:["user","company","government","admin"],children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"جاري تحميل الإعدادات..."})]})})}):(0,a.jsx)(p.default,{allowedRoles:["user","company","government","admin"],children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-gray-600 to-gray-800 text-white rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"الإعدادات"}),(0,a.jsx)("p",{className:"text-gray-100 mt-1",children:"إدارة تفضيلاتك وإعدادات الحساب"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(r.z,{onClick:T,variant:"outline",className:"bg-white/10 border-white/20 text-white hover:bg-white/20",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 ml-2"}),"تحديث"]}),(0,a.jsxs)(r.z,{onClick:O,disabled:o,className:"bg-white text-gray-800 hover:bg-gray-100",children:[o?(0,a.jsx)(g.Z,{className:"h-4 w-4 animate-spin ml-2"}):(0,a.jsx)(v.Z,{className:"h-4 w-4 ml-2"}),"حفظ التغييرات"]})]})]})}),(0,a.jsxs)(h.mQ,{defaultValue:"notifications",className:"space-y-6",children:[(0,a.jsxs)(h.dr,{className:"grid w-full grid-cols-4",children:[(0,a.jsxs)(h.SP,{value:"notifications",className:"flex items-center gap-2",children:[(0,a.jsx)(y.Z,{className:"h-4 w-4"}),"الإشعارات"]}),(0,a.jsxs)(h.SP,{value:"privacy",className:"flex items-center gap-2",children:[(0,a.jsx)(N.Z,{className:"h-4 w-4"}),"الخصوصية"]}),(0,a.jsxs)(h.SP,{value:"preferences",className:"flex items-center gap-2",children:[(0,a.jsx)(w.Z,{className:"h-4 w-4"}),"التفضيلات"]}),(0,a.jsxs)(h.SP,{value:"security",className:"flex items-center gap-2",children:[(0,a.jsx)(b.Z,{className:"h-4 w-4"}),"الأمان"]})]}),(0,a.jsx)(h.nU,{value:"notifications",children:(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(y.Z,{className:"h-5 w-5"}),"إعدادات الإشعارات"]}),(0,a.jsx)(n.SZ,{children:"اختر كيف ومتى تريد استلام الإشعارات"})]}),(0,a.jsxs)(n.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"طرق الإشعار"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"email-notifications",children:"البريد الإلكتروني"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"استلام الإشعارات عبر البريد الإلكتروني"})]}),(0,a.jsx)(u,{id:"email-notifications",checked:null==e?void 0:e.notifications.email,onCheckedChange:e=>D("notifications","email",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"sms-notifications",children:"الرسائل النصية"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"استلام الإشعارات عبر الرسائل النصية"})]}),(0,a.jsx)(u,{id:"sms-notifications",checked:null==e?void 0:e.notifications.sms,onCheckedChange:e=>D("notifications","sms",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"push-notifications",children:"الإشعارات الفورية"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"استلام الإشعارات الفورية في المتصفح"})]}),(0,a.jsx)(u,{id:"push-notifications",checked:null==e?void 0:e.notifications.push,onCheckedChange:e=>D("notifications","push",e)})]})]})]}),(0,a.jsx)(x.Z,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"أنواع الإشعارات"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"new-bids",children:"مزايدات جديدة"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"عند تقديم مزايدات جديدة على مزاداتك"})]}),(0,a.jsx)(u,{id:"new-bids",checked:null==e?void 0:e.notifications.newBids,onCheckedChange:e=>D("notifications","newBids",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"auction-ending",children:"انتهاء المزادات"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"تذكير قبل انتهاء المزادات"})]}),(0,a.jsx)(u,{id:"auction-ending",checked:null==e?void 0:e.notifications.auctionEnding,onCheckedChange:e=>D("notifications","auctionEnding",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"application-updates",children:"تحديثات الطلبات"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"عند تحديث حالة طلباتك في المناقصات"})]}),(0,a.jsx)(u,{id:"application-updates",checked:null==e?void 0:e.notifications.applicationUpdates,onCheckedChange:e=>D("notifications","applicationUpdates",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"payment-reminders",children:"تذكير المدفوعات"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"تذكير بالمدفوعات المستحقة"})]}),(0,a.jsx)(u,{id:"payment-reminders",checked:null==e?void 0:e.notifications.paymentReminders,onCheckedChange:e=>D("notifications","paymentReminders",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"marketing-emails",children:"الرسائل التسويقية"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"استلام العروض والأخبار التسويقية"})]}),(0,a.jsx)(u,{id:"marketing-emails",checked:null==e?void 0:e.notifications.marketingEmails,onCheckedChange:e=>D("notifications","marketingEmails",e)})]})]})]})]})]})}),(0,a.jsx)(h.nU,{value:"privacy",children:(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(N.Z,{className:"h-5 w-5"}),"إعدادات الخصوصية"]}),(0,a.jsx)(n.SZ,{children:"تحكم في من يمكنه رؤية معلوماتك"})]}),(0,a.jsxs)(n.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"profile-visibility",children:"مستوى ظهور الملف الشخصي"}),(0,a.jsxs)(m.Ph,{value:null==e?void 0:e.privacy.profileVisibility,onValueChange:e=>D("privacy","profileVisibility",e),children:[(0,a.jsx)(m.i4,{className:"mt-2",children:(0,a.jsx)(m.ki,{})}),(0,a.jsxs)(m.Bw,{children:[(0,a.jsx)(m.Ql,{value:"public",children:"عام - يمكن للجميع رؤيته"}),(0,a.jsx)(m.Ql,{value:"contacts",children:"جهات الاتصال فقط"}),(0,a.jsx)(m.Ql,{value:"private",children:"خاص - مخفي عن الآخرين"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"show-email",children:"إظهار البريد الإلكتروني"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"السماح للآخرين برؤية بريدك الإلكتروني"})]}),(0,a.jsx)(u,{id:"show-email",checked:null==e?void 0:e.privacy.showEmail,onCheckedChange:e=>D("privacy","showEmail",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"show-phone",children:"إظهار رقم الهاتف"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"السماح للآخرين برؤية رقم هاتفك"})]}),(0,a.jsx)(u,{id:"show-phone",checked:null==e?void 0:e.privacy.showPhone,onCheckedChange:e=>D("privacy","showPhone",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"show-bid-history",children:"إظهار تاريخ المزايدات"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"السماح للآخرين برؤية تاريخ مزايداتك"})]}),(0,a.jsx)(u,{id:"show-bid-history",checked:null==e?void 0:e.privacy.showBidHistory,onCheckedChange:e=>D("privacy","showBidHistory",e)})]})]})]})]})}),(0,a.jsx)(h.nU,{value:"preferences",children:(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.Z,{className:"h-5 w-5"}),"التفضيلات العامة"]}),(0,a.jsx)(n.SZ,{children:"تخصيص تجربة استخدام المنصة"})]}),(0,a.jsx)(n.aY,{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"language",children:"اللغة"}),(0,a.jsxs)(m.Ph,{value:null==e?void 0:e.preferences.language,onValueChange:e=>D("preferences","language",e),children:[(0,a.jsx)(m.i4,{className:"mt-2",children:(0,a.jsx)(m.ki,{})}),(0,a.jsxs)(m.Bw,{children:[(0,a.jsx)(m.Ql,{value:"ar",children:"العربية"}),(0,a.jsx)(m.Ql,{value:"en",children:"English"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"currency",children:"العملة"}),(0,a.jsxs)(m.Ph,{value:null==e?void 0:e.preferences.currency,onValueChange:e=>D("preferences","currency",e),children:[(0,a.jsx)(m.i4,{className:"mt-2",children:(0,a.jsx)(m.ki,{})}),(0,a.jsxs)(m.Bw,{children:[(0,a.jsx)(m.Ql,{value:"SAR",children:"ريال سعودي (SAR)"}),(0,a.jsx)(m.Ql,{value:"USD",children:"دولار أمريكي (USD)"}),(0,a.jsx)(m.Ql,{value:"EUR",children:"يورو (EUR)"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"theme",children:"المظهر"}),(0,a.jsxs)(m.Ph,{value:null==e?void 0:e.preferences.theme,onValueChange:e=>D("preferences","theme",e),children:[(0,a.jsx)(m.i4,{className:"mt-2",children:(0,a.jsx)(m.ki,{})}),(0,a.jsxs)(m.Bw,{children:[(0,a.jsx)(m.Ql,{value:"light",children:"فاتح"}),(0,a.jsx)(m.Ql,{value:"dark",children:"داكن"}),(0,a.jsx)(m.Ql,{value:"auto",children:"تلقائي"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"items-per-page",children:"عدد العناصر في الصفحة"}),(0,a.jsxs)(m.Ph,{value:null==e?void 0:e.preferences.itemsPerPage.toString(),onValueChange:e=>D("preferences","itemsPerPage",parseInt(e)),children:[(0,a.jsx)(m.i4,{className:"mt-2",children:(0,a.jsx)(m.ki,{})}),(0,a.jsxs)(m.Bw,{children:[(0,a.jsx)(m.Ql,{value:"5",children:"5 عناصر"}),(0,a.jsx)(m.Ql,{value:"10",children:"10 عناصر"}),(0,a.jsx)(m.Ql,{value:"20",children:"20 عنصر"}),(0,a.jsx)(m.Ql,{value:"50",children:"50 عنصر"})]})]})]})]})})]})}),(0,a.jsx)(h.nU,{value:"security",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(b.Z,{className:"h-5 w-5"}),"إعدادات الأمان"]}),(0,a.jsx)(n.SZ,{children:"حماية حسابك وبياناتك"})]}),(0,a.jsxs)(n.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"two-factor",children:"المصادقة الثنائية"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"إضافة طبقة حماية إضافية لحسابك"})]}),(0,a.jsx)(u,{id:"two-factor",checked:null==e?void 0:e.security.twoFactorEnabled,onCheckedChange:e=>D("security","twoFactorEnabled",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"login-notifications",children:"إشعارات تسجيل الدخول"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"إشعار عند تسجيل الدخول من جهاز جديد"})]}),(0,a.jsx)(u,{id:"login-notifications",checked:null==e?void 0:e.security.loginNotifications,onCheckedChange:e=>D("security","loginNotifications",e)})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"session-timeout",children:"انتهاء الجلسة (بالدقائق)"}),(0,a.jsxs)(m.Ph,{value:null==e?void 0:e.security.sessionTimeout.toString(),onValueChange:e=>D("security","sessionTimeout",parseInt(e)),children:[(0,a.jsx)(m.i4,{className:"mt-2",children:(0,a.jsx)(m.ki,{})}),(0,a.jsxs)(m.Bw,{children:[(0,a.jsx)(m.Ql,{value:"15",children:"15 دقيقة"}),(0,a.jsx)(m.Ql,{value:"30",children:"30 دقيقة"}),(0,a.jsx)(m.Ql,{value:"60",children:"ساعة واحدة"}),(0,a.jsx)(m.Ql,{value:"120",children:"ساعتان"}),(0,a.jsx)(m.Ql,{value:"480",children:"8 ساعات"})]})]})]})]})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsx)(n.ll,{children:"تغيير كلمة المرور"}),(0,a.jsx)(n.SZ,{children:"تحديث كلمة مرور حسابك"})]}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"current-password",children:"كلمة المرور الحالية"}),(0,a.jsxs)("div",{className:"relative mt-2",children:[(0,a.jsx)(l.I,{id:"current-password",type:V?"text":"password",value:F,onChange:e=>E(e.target.value),placeholder:"أدخل كلمة المرور الحالية"}),(0,a.jsx)(r.z,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2",onClick:()=>A(!V),children:V?(0,a.jsx)(k.Z,{className:"h-4 w-4"}):(0,a.jsx)(C.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"new-password",children:"كلمة المرور الجديدة"}),(0,a.jsx)(l.I,{id:"new-password",type:V?"text":"password",value:Q,onChange:e=>B(e.target.value),placeholder:"أدخل كلمة المرور الجديدة",className:"mt-2"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c._,{htmlFor:"confirm-password",children:"تأكيد كلمة المرور"}),(0,a.jsx)(l.I,{id:"confirm-password",type:V?"text":"password",value:z,onChange:e=>U(e.target.value),placeholder:"أعد إدخال كلمة المرور الجديدة",className:"mt-2"})]}),(0,a.jsx)(r.z,{onClick:Y,disabled:!F||!Q||!z,children:"تغيير كلمة المرور"})]})]}),(0,a.jsxs)(n.Zb,{className:"border-red-200",children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"text-red-600 flex items-center gap-2",children:[(0,a.jsx)(S.Z,{className:"h-5 w-5"}),"منطقة الخطر"]}),(0,a.jsx)(n.SZ,{children:"إجراءات لا يمكن التراجع عنها"})]}),(0,a.jsxs)(n.aY,{children:[(0,a.jsxs)(r.z,{variant:"destructive",className:"w-full",children:[(0,a.jsx)(Z.Z,{className:"h-4 w-4 ml-2"}),"حذف الحساب نهائياً"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"سيتم حذف جميع بياناتك نهائياً ولا يمكن استرجاعها"})]})]})]})})]})]})})}},2014:function(e,s,t){"use strict";t.d(s,{default:function(){return l}});var a=t(57437),i=t(2265),n=t(99376),r=t(63119);function l(e){let{children:s,allowedRoles:t}=e,[l,c]=(0,i.useState)(null),[d,o]=(0,i.useState)(!0),u=(0,n.useRouter)();return((0,i.useEffect)(()=>{let e=localStorage.getItem("token"),s=localStorage.getItem("user");if(!e||!s){u.push("/auth/login");return}let a=JSON.parse(s);if(!t.includes(a.role)){switch(a.role){case"admin":case"super_admin":u.push("/admin/dashboard");break;case"company":u.push("/company/dashboard");break;case"individual":u.push("/user/dashboard");break;case"government":u.push("/government/dashboard");break;default:u.push("/auth/login")}return}if("admin"!==a.role&&"super_admin"!==a.role&&"approved"!==a.status){u.push("/account-status");return}c(a),o(!1)},[u,t]),d)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-muted-foreground",children:"جاري التحميل..."})]})}):l?(0,a.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,a.jsx)(r.Z,{userRole:l.role}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:(0,a.jsx)("div",{className:"p-6",children:s})})]}):null}},66070:function(e,s,t){"use strict";t.d(s,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return r},aY:function(){return o},ll:function(){return c}});var a=t(57437),i=t(2265),n=t(94508);let r=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...i})});r.displayName="Card";let l=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...i})});l.displayName="CardHeader";let c=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...i})});c.displayName="CardTitle";let d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...i})});d.displayName="CardDescription";let o=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...i})});o.displayName="CardContent",i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...i})}).displayName="CardFooter"},95186:function(e,s,t){"use strict";t.d(s,{I:function(){return r}});var a=t(57437),i=t(2265),n=t(94508);let r=i.forwardRef((e,s)=>{let{className:t,type:i,...r}=e;return(0,a.jsx)("input",{type:i,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...r})});r.displayName="Input"},26815:function(e,s,t){"use strict";t.d(s,{_:function(){return d}});var a=t(57437),i=t(2265),n=t(6394),r=t(90535),l=t(94508);let c=(0,r.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(n.f,{ref:s,className:(0,l.cn)(c(),t),...i})});d.displayName=n.f.displayName},53647:function(e,s,t){"use strict";t.d(s,{Bw:function(){return f},Ph:function(){return o},Ql:function(){return p},i4:function(){return m},ki:function(){return u}});var a=t(57437),i=t(2265),n=t(57864),r=t(40875),l=t(22135),c=t(30401),d=t(94508);let o=n.fC;n.ZA;let u=n.B4,m=i.forwardRef((e,s)=>{let{className:t,children:i,...l}=e;return(0,a.jsxs)(n.xz,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[i,(0,a.jsx)(n.JO,{asChild:!0,children:(0,a.jsx)(r.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=n.xz.displayName;let h=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(n.u_,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...i,children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})});h.displayName=n.u_.displayName;let x=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(n.$G,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...i,children:(0,a.jsx)(r.Z,{className:"h-4 w-4"})})});x.displayName=n.$G.displayName;let f=i.forwardRef((e,s)=>{let{className:t,children:i,position:r="popper",...l}=e;return(0,a.jsx)(n.h_,{children:(0,a.jsxs)(n.VY,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...l,children:[(0,a.jsx)(h,{}),(0,a.jsx)(n.l_,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:i}),(0,a.jsx)(x,{})]})})});f.displayName=n.VY.displayName,i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(n.__,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...i})}).displayName=n.__.displayName;let p=i.forwardRef((e,s)=>{let{className:t,children:i,...r}=e;return(0,a.jsxs)(n.ck,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.wU,{children:(0,a.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(n.eT,{children:i})]})});p.displayName=n.ck.displayName,i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(n.Z0,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...i})}).displayName=n.Z0.displayName},6512:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var a=t(57437),i=t(2265),n=t(55156),r=t(94508);let l=i.forwardRef((e,s)=>{let{className:t,orientation:i="horizontal",decorative:l=!0,...c}=e;return(0,a.jsx)(n.f,{ref:s,decorative:l,orientation:i,className:(0,r.cn)("shrink-0 bg-border","horizontal"===i?"h-[1px] w-full":"h-full w-[1px]",t),...c})});l.displayName=n.f.displayName},12339:function(e,s,t){"use strict";t.d(s,{SP:function(){return d},dr:function(){return c},mQ:function(){return l},nU:function(){return o}});var a=t(57437),i=t(2265),n=t(49707),r=t(94508);let l=n.fC,c=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(n.aV,{ref:s,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...i})});c.displayName=n.aV.displayName;let d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(n.xz,{ref:s,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...i})});d.displayName=n.xz.displayName;let o=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(n.VY,{ref:s,className:(0,r.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...i})});o.displayName=n.VY.displayName},24895:function(e,s,t){"use strict";t.d(s,{p:function(){return l}});var a=t(2265);let i={toasts:[]},n=[];function r(e){i=e,n.forEach(s=>s(e))}function l(){let[e,s]=(0,a.useState)(i);return(0,a.useEffect)(()=>(n.push(s),()=>{n=n.filter(e=>e!==s)}),[]),{toast:e=>{let{...s}=e;console.log("Toast called with:",s);let t=Math.random().toString(36).substr(2,9),a={...s,id:t},n={...i,toasts:[...i.toasts,a]};return console.log("Updating toast state with:",n),r(n),setTimeout(()=>{r({...i,toasts:i.toasts.filter(e=>e.id!==t)})},8e3),{id:t,dismiss:()=>{r({...i,toasts:i.toasts.filter(e=>e.id!==t)})}}},toasts:e.toasts}}},29116:function(e,s,t){"use strict";t.d(s,{Sb:function(){return l},Xy:function(){return r},kv:function(){return i},zg:function(){return n}});let a=t(83464).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>{let s=localStorage.getItem("token");return s&&(e.headers.Authorization="Bearer ".concat(s)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>{var s;return(null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)});let i={register:e=>a.post("/auth/register",e),login:e=>a.post("/auth/login",e),logout:()=>a.post("/auth/logout"),verifyEmail:e=>a.post("/auth/verify-email",{token:e}),resendVerification:e=>a.post("/auth/resend-verification",{email:e}),forgotPassword:e=>a.post("/auth/forgot-password",{email:e}),resetPassword:e=>a.post("/auth/reset-password",e)},n={getAll:()=>a.get("/auctions"),getById:e=>a.get("/auctions/".concat(e)),create:e=>a.post("/auctions",e),update:(e,s)=>a.put("/auctions/".concat(e),s),delete:e=>a.delete("/auctions/".concat(e)),placeBid:(e,s)=>a.post("/auctions/".concat(e,"/bid"),{amount:s})},r={getFavorites:e=>a.get("/favorites",{params:e}),addFavorite:e=>a.post("/favorites",e),removeFavorite:(e,s)=>a.delete("/favorites/".concat(e,"/").concat(s)),updateFavorite:(e,s,t)=>a.put("/favorites/".concat(e,"/").concat(s),t),checkFavorite:(e,s)=>a.get("/favorites/check/".concat(e,"/").concat(s))},l={getDashboardStats:()=>a.get("/admin/dashboard"),getPendingAccounts:()=>a.get("/admin/pending-accounts"),approvePendingAccount:e=>a.post("/admin/pending-accounts/".concat(e,"/approve")),rejectPendingAccount:(e,s)=>a.post("/admin/pending-accounts/".concat(e,"/reject"),{reason:s}),users:{getAll:e=>a.get("/admin/users",{params:e}),getById:e=>a.get("/admin/users/".concat(e)),update:(e,s)=>a.put("/admin/users/".concat(e),s),delete:e=>a.delete("/admin/users/".concat(e)),activate:e=>a.post("/admin/users/".concat(e,"/activate")),deactivate:e=>a.post("/admin/users/".concat(e,"/deactivate"))},auctions:{getAll:e=>a.get("/admin/auctions",{params:e}),getById:e=>a.get("/admin/auctions/".concat(e)),approve:e=>a.post("/admin/auctions/".concat(e,"/approve")),reject:(e,s)=>a.post("/admin/auctions/".concat(e,"/reject"),{reason:s}),suspend:(e,s)=>a.post("/admin/auctions/".concat(e,"/suspend"),{reason:s}),delete:e=>a.delete("/admin/auctions/".concat(e))},tenders:{getAll:e=>a.get("/admin/tenders",{params:e}),getById:e=>a.get("/admin/tenders/".concat(e)),approve:e=>a.post("/admin/tenders/".concat(e,"/approve")),reject:(e,s)=>a.post("/admin/tenders/".concat(e,"/reject"),{reason:s}),suspend:(e,s)=>a.post("/admin/tenders/".concat(e,"/suspend"),{reason:s}),delete:e=>a.delete("/admin/tenders/".concat(e))},getTender:e=>a.get("/admin/tenders/".concat(e)),getTenderSubmissions:(e,s)=>a.get("/admin/tenders/".concat(e,"/submissions"),{params:s}),updateTenderStatus:(e,s)=>a.put("/admin/tenders/".concat(e,"/status"),s),updateTenderSubmissionStatus:(e,s,t)=>a.put("/admin/tenders/".concat(e,"/submissions/").concat(s,"/status"),t),reports:{getFinancialReport:e=>a.get("/admin/reports/financial",{params:e}),getUserReport:e=>a.get("/admin/reports/users",{params:e}),getActivityReport:e=>a.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>a.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>a.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>a.get("/admin/settings"),update:e=>a.put("/admin/settings",e),backup:()=>a.post("/admin/settings/backup"),restore:e=>a.post("/admin/settings/restore/".concat(e))}};s.ZP=a}},function(e){e.O(0,[5554,3464,5650,6052,4863,7201,6404,3119,2971,2117,1744],function(){return e(e.s=89548)}),_N_E=e.O()}]);