(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{39068:function(e,t,r){Promise.resolve().then(r.bind(r,59556)),Promise.resolve().then(r.bind(r,46281)),Promise.resolve().then(r.t.bind(r,44742,23)),Promise.resolve().then(r.t.bind(r,47960,23))},59556:function(e,t,r){"use strict";r.d(t,{Toaster:function(){return v}});var o=r(57437),n=r(2265),s=r(90535),a=r(32489),u=r(94508);let i=(0,s.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all duration-300 animate-in slide-in-from-top-2",{variants:{variant:{default:"border bg-background text-foreground",destructive:"border-red-500 bg-red-500 text-white"}},defaultVariants:{variant:"default"}}),l=n.forwardRef((e,t)=>{let{className:r,variant:n,...s}=e;return(0,o.jsx)("div",{ref:t,className:(0,u.cn)(i({variant:n}),r),...s})});l.displayName="Toast",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("button",{ref:t,className:(0,u.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...n})}).displayName="ToastAction";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("button",{ref:t,className:(0,u.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/70 opacity-100 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-[.destructive]:text-white/70 group-[.destructive]:hover:text-white",r),"toast-close":"",...n,children:(0,o.jsx)(a.Z,{className:"h-4 w-4"})})});c.displayName="ToastClose";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("div",{ref:t,className:(0,u.cn)("text-sm font-semibold",r),...n})});d.displayName="ToastTitle";let f=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)("div",{ref:t,className:(0,u.cn)("text-sm opacity-90",r),...n})});f.displayName="ToastDescription";var m=r(24895);function v(){let{toasts:e}=(0,m.p)();return console.log("Toaster rendering with toasts:",e),(0,o.jsx)("div",{className:"fixed top-0 right-0 z-[100] flex max-h-screen w-full max-w-[420px] flex-col p-4 space-y-2",children:e.map(function(e){let{id:t,title:r,description:n,action:s,...a}=e;return(0,o.jsxs)(l,{...a,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[r&&(0,o.jsx)(d,{children:r}),n&&(0,o.jsx)(f,{children:n})]}),s,(0,o.jsx)(c,{})]},t)})})}},24895:function(e,t,r){"use strict";r.d(t,{p:function(){return u}});var o=r(2265);let n={toasts:[]},s=[];function a(e){n=e,s.forEach(t=>t(e))}function u(){let[e,t]=(0,o.useState)(n);return(0,o.useEffect)(()=>(s.push(t),()=>{s=s.filter(e=>e!==t)}),[]),{toast:e=>{let{...t}=e;console.log("Toast called with:",t);let r=Math.random().toString(36).substr(2,9),o={...t,id:r},s={...n,toasts:[...n.toasts,o]};return console.log("Updating toast state with:",s),a(s),setTimeout(()=>{a({...n,toasts:n.toasts.filter(e=>e.id!==r)})},8e3),{id:r,dismiss:()=>{a({...n,toasts:n.toasts.filter(e=>e.id!==r)})}}},toasts:e.toasts}}},46281:function(e,t,r){"use strict";r.d(t,{AuthProvider:function(){return u},a:function(){return i}});var o=r(57437),n=r(2265),s=r(99376);let a=(0,n.createContext)(void 0);function u(e){let{children:t}=e,[r,u]=(0,n.useState)(null),[i,l]=(0,n.useState)(null),[c,d]=(0,n.useState)(!0),f=(0,s.useRouter)();return(0,n.useEffect)(()=>{(()=>{try{let e=localStorage.getItem("token"),t=localStorage.getItem("user");e&&t&&(l(e),u(JSON.parse(t)))}catch(e){console.error("Error initializing auth:",e),localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user")}finally{d(!1)}})()},[]),(0,o.jsx)(a.Provider,{value:{user:r,token:i,isLoading:c,login:(e,t,r)=>{l(e),u(r),localStorage.setItem("token",e),localStorage.setItem("refreshToken",t),localStorage.setItem("user",JSON.stringify(r))},logout:()=>{l(null),u(null),localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user"),f.push("/auth/login")},isAuthenticated:!!i&&!!r},children:t})}function i(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},94508:function(e,t,r){"use strict";r.d(t,{cn:function(){return s}});var o=r(61994),n=r(53335);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,o.W)(t))}},32489:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});let o=(0,r(39763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},99376:function(e,t,r){"use strict";var o=r(35475);r.o(o,"useParams")&&r.d(t,{useParams:function(){return o.useParams}}),r.o(o,"usePathname")&&r.d(t,{usePathname:function(){return o.usePathname}}),r.o(o,"useRouter")&&r.d(t,{useRouter:function(){return o.useRouter}}),r.o(o,"useSearchParams")&&r.d(t,{useSearchParams:function(){return o.useSearchParams}})},47960:function(){},44742:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},90535:function(e,t,r){"use strict";r.d(t,{j:function(){return a}});var o=r(61994);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=o.W,a=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:u}=t,i=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],o=null==u?void 0:u[e];if(null===t)return null;let s=n(t)||n(o);return a[e][s]}),l=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return s(e,i,null==t?void 0:null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...u,...l}[t]):({...u,...l})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[3042,3587,5554,2971,2117,1744],function(){return e(e.s=39068)}),_N_E=e.O()}]);