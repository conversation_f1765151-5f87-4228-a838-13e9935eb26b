(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6974],{59479:function(e,r,t){Promise.resolve().then(t.bind(t,33982))},33982:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return b}});var s=t(57437),n=t(2265),a=t(66070),i=t(62869),l=t(35974),d=t(60610),c=t(94508);let o=n.forwardRef((e,r)=>{let{className:t,value:n,...a}=e;return(0,s.jsx)(d.fC,{ref:r,className:(0,c.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...a,children:(0,s.jsx)(d.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(n||0),"%)")}})})});o.displayName=d.fC.displayName;var u=t(92369),m=t(60044),x=t(88906),f=t(65302),p=t(45131),h=t(22252),g=t(48736),j=t(17689),v=t(99376);let N={individual:[{id:"national_id",name:"الهوية الوطنية/الإقامة",required:!0,description:"صورة واضحة من الوجهين"},{id:"personal_photo",name:"صورة شخصية",required:!0,description:"صورة حديثة وواضحة"},{id:"address_proof",name:"إثبات العنوان",required:!1,description:"فاتورة كهرباء أو ماء حديثة"}],company:[{id:"commercial_register",name:"السجل التجاري",required:!0,description:"السجل التجاري ساري المفعول"},{id:"professional_license",name:"رخصة المهنة",required:!0,description:"رخصة المهنة سارية المفعول"},{id:"tax_certificate",name:"شهادة الزكاة والضريبة",required:!0,description:"شهادة سارية المفعول"},{id:"establishment_contract",name:"عقد التأسيس",required:!0,description:"عقد تأسيس الشركة"},{id:"authorized_signature",name:"صورة المفوض بالتوقيع",required:!0,description:"صورة هوية المفوض بالتوقيع"}],government:[{id:"official_authorization",name:"التفويض الرسمي",required:!0,description:"التفويض الرسمي من الجهة"},{id:"employee_id",name:"هوية الموظف المخول",required:!0,description:"هوية الموظف المخول بالتوقيع"},{id:"official_letter",name:"خطاب رسمي من الجهة",required:!0,description:"خطاب رسمي معتمد"},{id:"government_id",name:"رقم الجهة الحكومية",required:!0,description:"وثيقة تثبت رقم الجهة الحكومية"}]};function b(){let[e,r]=(0,n.useState)(null),[t,l]=(0,n.useState)({}),[d,c]=(0,n.useState)({}),[f,p]=(0,n.useState)({}),[h,g]=(0,n.useState)(!1),j=(0,v.useRouter)();(0,n.useEffect)(()=>{let e=localStorage.getItem("user");e?r(JSON.parse(e)):j.push("/auth/login")},[j]);let b=(e,r)=>{if(!["image/jpeg","image/png","image/jpg","application/pdf"].includes(r.type)){alert("نوع الملف غير مدعوم. يرجى رفع صور (JPG, PNG) أو ملفات PDF فقط.");return}if(r.size>10485760){alert("حجم الملف كبير جداً. الحد الأقصى هو 10 ميجابايت.");return}l(t=>({...t,[e]:r})),p(r=>({...r,[e]:"pending"}))},w=async e=>{if(t[e]){p(r=>({...r,[e]:"uploading"})),c(r=>({...r,[e]:0}));try{for(let r=0;r<=100;r+=10)c(t=>({...t,[e]:r})),await new Promise(e=>setTimeout(e,100));await new Promise(e=>setTimeout(e,500)),p(r=>({...r,[e]:"success"}))}catch(r){p(r=>({...r,[e]:"error"}))}}},_=e=>{l(r=>{let t={...r};return delete t[e],t}),p(r=>{let t={...r};return delete t[e],t}),c(r=>{let t={...r};return delete t[e],t})},C=async()=>{g(!0);try{for(let e of Object.keys(t).filter(e=>"pending"===f[e]))await w(e);await new Promise(e=>setTimeout(e,1e3));let s={...e,status:"documents_submitted"};r(s),localStorage.setItem("user",JSON.stringify(s)),alert("تم رفع الوثائق بنجاح! سيتم مراجعة حسابك خلال 3 أيام عمل."),j.push("/account-status")}catch(e){alert("حدث خطأ أثناء رفع الوثائق. يرجى المحاولة مرة أخرى.")}finally{g(!1)}};if(!e)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-muted-foreground",children:"جاري التحميل..."})]})});let q=N[e.role]||[],k=q.filter(e=>e.required),z=q.filter(e=>!e.required),S=k.filter(e=>"success"===f[e.id]).length,Z=S/k.length*100;return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 max-w-4xl",children:[(0,s.jsxs)("header",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"رفع الوثائق المطلوبة"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"يرجى رفع الوثائق المطلوبة لإتمام عملية تفعيل الحساب"})]}),(0,s.jsxs)(a.Zb,{className:"mb-8",children:[(0,s.jsx)(a.Ol,{children:(0,s.jsxs)(a.ll,{className:"flex items-center gap-2",children:[(e=>{switch(e){case"individual":return(0,s.jsx)(u.Z,{className:"h-6 w-6 text-purple-500"});case"company":return(0,s.jsx)(m.Z,{className:"h-6 w-6 text-green-500"});case"government":return(0,s.jsx)(x.Z,{className:"h-6 w-6 text-red-500"});default:return(0,s.jsx)(u.Z,{className:"h-6 w-6"})}})(e.role),"معلومات الحساب"]})}),(0,s.jsx)(a.aY,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"البريد الإلكتروني"}),(0,s.jsx)("p",{className:"font-medium",children:e.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"نوع الحساب"}),(0,s.jsx)("p",{className:"font-medium",children:(e=>{switch(e){case"individual":return"فرد";case"company":return"شركة";case"government":return"جهة حكومية";default:return e}})(e.role)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"التقدم"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o,{value:Z,className:"flex-1"}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[Math.round(Z),"%"]})]})]})]})})]}),(0,s.jsxs)(a.Zb,{className:"mb-8",children:[(0,s.jsx)(a.Ol,{children:(0,s.jsx)(a.ll,{className:"text-red-600",children:"الوثائق المطلوبة *"})}),(0,s.jsx)(a.aY,{className:"space-y-6",children:k.map(e=>(0,s.jsx)(y,{document:e,uploadedFile:t[e.id],uploadProgress:d[e.id],uploadStatus:f[e.id],onFileSelect:r=>b(e.id,r),onUpload:()=>w(e.id),onRemove:()=>_(e.id)},e.id))})]}),z.length>0&&(0,s.jsxs)(a.Zb,{className:"mb-8",children:[(0,s.jsx)(a.Ol,{children:(0,s.jsx)(a.ll,{className:"text-blue-600",children:"الوثائق الاختيارية"})}),(0,s.jsx)(a.aY,{className:"space-y-6",children:z.map(e=>(0,s.jsx)(y,{document:e,uploadedFile:t[e.id],uploadProgress:d[e.id],uploadStatus:f[e.id],onFileSelect:r=>b(e.id,r),onUpload:()=>w(e.id),onRemove:()=>_(e.id)},e.id))})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(i.z,{onClick:C,disabled:S<k.length||h,size:"lg",className:"w-full md:w-auto px-8",children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"جاري الإرسال..."]}):"إرسال الوثائق للمراجعة"}),S<k.length&&(0,s.jsx)("p",{className:"text-sm text-red-600 mt-2",children:"يجب رفع جميع الوثائق المطلوبة أولاً"})]})]})})}function y(e){let{document:r,uploadedFile:t,uploadProgress:n=0,uploadStatus:a="pending",onFileSelect:d,onUpload:c,onRemove:u}=e;return(0,s.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)("h3",{className:"font-medium",children:r.name}),r.required&&(0,s.jsx)(l.C,{variant:"destructive",className:"text-xs",children:"مطلوب"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:r.description})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(a){case"success":return(0,s.jsx)(f.Z,{className:"h-5 w-5 text-green-500"});case"error":return(0,s.jsx)(p.Z,{className:"h-5 w-5 text-red-500"});case"uploading":return(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"});default:return(0,s.jsx)(h.Z,{className:"h-5 w-5 text-yellow-500"})}})(),(0,s.jsx)("span",{className:"text-sm",children:(()=>{switch(a){case"success":return"تم الرفع بنجاح";case"error":return"خطأ في الرفع";case"uploading":return"جاري الرفع... ".concat(n,"%");default:return"في انتظار الرفع"}})()})]})]}),t?(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded",children:[(0,s.jsx)(g.Z,{className:"h-5 w-5 text-blue-500"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"font-medium text-sm",children:t.name}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(t.size/1024/1024).toFixed(2)," ميجابايت"]})]}),(0,s.jsx)(i.z,{variant:"outline",size:"sm",onClick:u,children:"إزالة"})]}),"uploading"===a&&(0,s.jsx)(o,{value:n,className:"w-full"}),"pending"===a&&(0,s.jsx)(i.z,{onClick:c,size:"sm",children:"رفع الملف"})]}):(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-blue-400 transition-colors",onClick:()=>{let e=r.createElement("input");e.type="file",e.accept="image/*,.pdf",e.onchange=e=>{var r;let t=null===(r=e.target.files)||void 0===r?void 0:r[0];t&&d(t)},e.click()},children:[(0,s.jsx)(j.Z,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"انقر لاختيار الملف"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"أو اسحب الملف هنا"})]})]})}},35974:function(e,r,t){"use strict";t.d(r,{C:function(){return l}});var s=t(57437);t(2265);var n=t(90535),a=t(94508);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:t,...n}=e;return(0,s.jsx)("div",{className:(0,a.cn)(i({variant:t}),r),...n})}},62869:function(e,r,t){"use strict";t.d(r,{z:function(){return c}});var s=t(57437),n=t(2265),a=t(37053),i=t(90535),l=t(94508);let d=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,r)=>{let{className:t,variant:n,size:i,asChild:c=!1,...o}=e,u=c?a.g7:"button";return(0,s.jsx)(u,{className:(0,l.cn)(d({variant:n,size:i,className:t})),ref:r,...o})});c.displayName="Button"},66070:function(e,r,t){"use strict";t.d(r,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return i},aY:function(){return o},ll:function(){return d}});var s=t(57437),n=t(2265),a=t(94508);let i=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});i.displayName="Card";let l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...n})});l.displayName="CardHeader";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});d.displayName="CardTitle";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",t),...n})});c.displayName="CardDescription";let o=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...n})});o.displayName="CardContent",n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"},94508:function(e,r,t){"use strict";t.d(r,{cn:function(){return a}});var s=t(61994),n=t(53335);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.m6)((0,s.W)(r))}}},function(e){e.O(0,[5554,7524,2971,2117,1744],function(){return e(e.s=59479)}),_N_E=e.O()}]);