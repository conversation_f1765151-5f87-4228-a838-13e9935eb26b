"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1901],{17008:function(e,r,t){t.d(r,{$:function(){return er},u:function(){return ee}});var n=t(2265),a=t(61994),i=t(9841),o=t(13137),l=t(20407),u=t(58772),c=t(16630),s=t(82944),f=t(34067),p=t(49037),v=t(41637),d=t(11638),y=["x","y"];function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(null,arguments)}function b(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function h(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?b(Object(t),!0).forEach(function(r){var n,a;n=r,a=t[r],(n=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(n))in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):b(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function g(e,r){var{x:t,y:n}=e,a=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==r.indexOf(n))continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],-1===r.indexOf(t)&&({}).propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,y),i=parseInt("".concat(t),10),o=parseInt("".concat(n),10),l=parseInt("".concat(r.height||a.height),10),u=parseInt("".concat(r.width||a.width),10);return h(h(h(h(h({},r),a),i?{x:i}:{}),o?{y:o}:{}),{},{height:l,width:u,name:r.name,radius:r.radius})}function x(e){return n.createElement(d.b,m({shapeType:"rectangle",propTransformer:g,activeClassName:"recharts-active-bar"},e))}var O=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(t,n)=>{if((0,c.hj)(e))return e;var a=(0,c.hj)(t)||(0,c.Rw)(t);return a?e(t,n):(a||function(e,r){if(!e)throw Error("Invariant failed")}(!1),r)}},P=t(44296),E=t(35623),w=t(35450),j=t(87279),A=t(45702),k=t(35953),S=t(9145),I=t(39040),z=t(58735),K=t(31944),C=t(62658),D=t(59087),M=t(40130),B=t(46595),N=["onMouseEnter","onMouseLeave","onClick"],L=["value","background","tooltipPosition"],Y=["onMouseEnter","onClick","onMouseLeave"];function T(){return(T=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(null,arguments)}function R(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function F(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?R(Object(t),!0).forEach(function(r){V(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):R(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function V(e,r,t){var n;return(r="symbol"==typeof(n=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(r,"string"))?n:n+"")in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function _(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==r.indexOf(n))continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],-1===r.indexOf(t)&&({}).propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}var W=e=>{var{dataKey:r,name:t,fill:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:r,type:a,color:n,value:(0,p.hn)(t,r),payload:e}]};function $(e){var{dataKey:r,stroke:t,strokeWidth:n,fill:a,name:i,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:t,strokeWidth:n,fill:a,dataKey:r,nameKey:void 0,name:(0,p.hn)(i,r),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function X(e){var r=(0,I.C)(K.Ve),{data:t,dataKey:a,background:i,allOtherBarProps:o}=e,{onMouseEnter:l,onMouseLeave:u,onClick:c}=o,f=_(o,N),p=(0,P.Df)(l,a),d=(0,P.oQ)(u),y=(0,P.nC)(c,a);if(!i||null==t)return null;var m=(0,s.L6)(i,!1);return n.createElement(n.Fragment,null,t.map((e,t)=>{var{value:o,background:l,tooltipPosition:u}=e,c=_(e,L);if(!l)return null;var s=p(e,t),b=d(e,t),h=y(e,t),g=F(F(F(F(F({option:i,isActive:String(t)===r},c),{},{fill:"#eee"},l),m),(0,v.bw)(f,e,t)),{},{onMouseEnter:s,onMouseLeave:b,onClick:h,dataKey:a,index:t,className:"recharts-bar-background-rectangle"});return n.createElement(x,T({key:"background-bar-".concat(t)},g))}))}function q(e){var{data:r,props:t,showLabels:a}=e,o=(0,s.L6)(t,!1),{shape:l,dataKey:c,activeBar:f}=t,p=(0,I.C)(K.Ve),d=(0,I.C)(K.du),{onMouseEnter:y,onClick:m,onMouseLeave:b}=t,h=_(t,Y),g=(0,P.Df)(y,c),O=(0,P.oQ)(b),E=(0,P.nC)(m,c);return r?n.createElement(n.Fragment,null,r.map((e,r)=>{var t=f&&String(r)===p&&(null==d||c===d),a=F(F(F({},o),e),{},{isActive:t,option:t?f:l,index:r,dataKey:c});return n.createElement(i.m,T({className:"recharts-bar-rectangle"},(0,v.bw)(h,e,r),{onMouseEnter:g(e,r),onMouseLeave:O(e,r),onClick:E(e,r),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),n.createElement(x,a))}),a&&u.e.renderCallByParent(t,r)):null}function Q(e){var{props:r,previousRectanglesRef:t}=e,{data:a,layout:o,isAnimationActive:l,animationBegin:u,animationDuration:s,animationEasing:f,onAnimationEnd:p,onAnimationStart:v}=r,d=t.current,y=(0,D.i)(r,"recharts-bar-"),[m,b]=(0,n.useState)(!1),h=(0,n.useCallback)(()=>{"function"==typeof p&&p(),b(!1)},[p]),g=(0,n.useCallback)(()=>{"function"==typeof v&&v(),b(!0)},[v]);return n.createElement(B.r,{begin:u,duration:s,isActive:l,easing:f,from:{t:0},to:{t:1},onAnimationEnd:h,onAnimationStart:g,key:y},e=>{var{t:l}=e,u=1===l?a:a.map((e,r)=>{var t=d&&d[r];if(t){var n=(0,c.k4)(t.x,e.x),a=(0,c.k4)(t.y,e.y),i=(0,c.k4)(t.width,e.width),u=(0,c.k4)(t.height,e.height);return F(F({},e),{},{x:n(l),y:a(l),width:i(l),height:u(l)})}if("horizontal"===o){var s=(0,c.k4)(0,e.height)(l);return F(F({},e),{},{y:e.y+e.height-s,height:s})}var f=(0,c.k4)(0,e.width)(l);return F(F({},e),{},{width:f})});return l>0&&(t.current=u),n.createElement(i.m,null,n.createElement(q,{props:r,data:u,showLabels:!m}))})}function U(e){var{data:r,isAnimationActive:t}=e,a=(0,n.useRef)(null);return t&&r&&r.length&&(null==a.current||a.current!==r)?n.createElement(Q,{previousRectanglesRef:a,props:e}):n.createElement(q,{props:e,data:r,showLabels:!0})}var G=(e,r)=>{var t=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:t,errorVal:(0,p.F$)(e,r)}};class J extends n.PureComponent{render(){var{hide:e,data:r,dataKey:t,className:l,xAxisId:u,yAxisId:s,needClip:f,background:p,id:v,layout:d}=this.props;if(e)return null;var y=(0,a.W)("recharts-bar",l),m=(0,c.Rw)(v)?this.id:v;return n.createElement(i.m,{className:y},f&&n.createElement("defs",null,n.createElement(A.W,{clipPathId:m,xAxisId:u,yAxisId:s})),n.createElement(i.m,{className:"recharts-bar-rectangles",clipPath:f?"url(#clipPath-".concat(m,")"):null},n.createElement(X,{data:r,dataKey:t,background:p,allOtherBarProps:this.props}),n.createElement(U,this.props)),n.createElement(o.D,{direction:"horizontal"===d?"y":"x"},this.props.children))}constructor(){super(...arguments),V(this,"id",(0,c.EL)("recharts-bar-"))}}var H={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!f.x.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function Z(e){var r,{xAxisId:t,yAxisId:a,hide:i,legendType:o,minPointSize:u,activeBar:c,animationBegin:f,animationDuration:v,animationEasing:d,isAnimationActive:y}=(0,M.j)(e,H),{needClip:m}=(0,A.N)(t,a),b=(0,k.vn)(),h=(0,z.W)(),g=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:u,stackId:(0,p.GA)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,u,e.stackId]),x=(0,s.NN)(e.children,l.b),O=(0,I.C)(e=>(0,S.iR)(e,t,a,h,g,x));if("vertical"!==b&&"horizontal"!==b)return null;var P=null==O?void 0:O[0];return r=null==P||null==P.height||null==P.width?0:"vertical"===b?P.height/2:P.width/2,n.createElement(j.zU,{xAxisId:t,yAxisId:a,data:O,dataPointFormatter:G,errorBarOffset:r},n.createElement(J,T({},e,{layout:b,needClip:m,data:O,xAxisId:t,yAxisId:a,hide:i,legendType:o,minPointSize:u,activeBar:c,animationBegin:f,animationDuration:v,animationEasing:d,isAnimationActive:y})))}function ee(e){var{layout:r,barSettings:{dataKey:t,minPointSize:n},pos:a,bandSize:i,xAxis:o,yAxis:l,xAxisTicks:u,yAxisTicks:s,stackedData:f,displayedData:v,offset:d,cells:y}=e,m="horizontal"===r?l:o,b=f?m.scale.domain():null,h=(0,p.Yj)({numericAxis:m});return v.map((e,v)=>{f?g=(0,p.Vv)(f[v],b):Array.isArray(g=(0,p.F$)(e,t))||(g=[h,g]);var m=O(n,0)(g[1],v);if("horizontal"===r){var g,x,P,E,w,j,A,[k,S]=[l.scale(g[0]),l.scale(g[1])];x=(0,p.Fy)({axis:o,ticks:u,bandSize:i,offset:a.offset,entry:e,index:v}),P=null!==(A=null!=S?S:k)&&void 0!==A?A:void 0,E=a.size;var I=k-S;if(w=(0,c.In)(I)?0:I,j={x,y:d.top,width:E,height:d.height},Math.abs(m)>0&&Math.abs(w)<Math.abs(m)){var z=(0,c.uY)(w||m)*(Math.abs(m)-Math.abs(w));P-=z,w+=z}}else{var[K,C]=[o.scale(g[0]),o.scale(g[1])];if(x=K,P=(0,p.Fy)({axis:l,ticks:s,bandSize:i,offset:a.offset,entry:e,index:v}),E=C-K,w=a.size,j={x:d.left,y:P,width:d.width,height:w},Math.abs(m)>0&&Math.abs(E)<Math.abs(m)){var D=(0,c.uY)(E||m)*(Math.abs(m)-Math.abs(E));E+=D}}return F(F({},e),{},{x,y:P,width:E,height:w,value:f?g:g[1],payload:e,background:j,tooltipPosition:{x:x+E/2,y:P+w/2}},y&&y[v]&&y[v].props)})}class er extends n.PureComponent{render(){return n.createElement(j.Ph,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(w.Y,null),n.createElement(C.L,{legendPayload:W(this.props)}),n.createElement(E.k,{fn:$,args:this.props}),n.createElement(Z,this.props))}}V(er,"displayName","Bar"),V(er,"defaultProps",H)},13137:function(e,r,t){t.d(r,{D:function(){return y}});var n=t(2265),a=t(9841),i=t(82944),o=t(87279),l=t(7986),u=t(40130),c=t(46595),s=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function f(e,r,t){var n;return(r="symbol"==typeof(n=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(r,"string"))?n:n+"")in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(null,arguments)}function v(e){var{direction:r,width:t,dataKey:u,isAnimationActive:f,animationBegin:v,animationDuration:d,animationEasing:y}=e,m=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==r.indexOf(n))continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],-1===r.indexOf(t)&&({}).propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,s),b=(0,i.L6)(m,!1),{data:h,dataPointFormatter:g,xAxisId:x,yAxisId:O,errorBarOffset:P}=(0,o.$n)(),E=(0,l.X_)(x),w=(0,l.nV)(O);if((null==E?void 0:E.scale)==null||(null==w?void 0:w.scale)==null||null==h||"x"===r&&"number"!==E.type)return null;var j=h.map(e=>{var i,o,{x:l,y:s,value:m,errorVal:h}=g(e,u,r);if(!h)return null;var x=[];if(Array.isArray(h)?[i,o]=h:i=o=h,"x"===r){var{scale:O}=E,j=s+P,A=j+t,k=j-t,S=O(m-i),I=O(m+o);x.push({x1:I,y1:A,x2:I,y2:k}),x.push({x1:S,y1:j,x2:I,y2:j}),x.push({x1:S,y1:A,x2:S,y2:k})}else if("y"===r){var{scale:z}=w,K=l+P,C=K-t,D=K+t,M=z(m-i),B=z(m+o);x.push({x1:C,y1:B,x2:D,y2:B}),x.push({x1:K,y1:M,x2:K,y2:B}),x.push({x1:C,y1:M,x2:D,y2:M})}var N="".concat(l+P,"px ").concat(s+P,"px");return n.createElement(a.m,p({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},b),x.map(e=>{var r=f?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(c.r,{from:{transform:"scaleY(0)",transformOrigin:N},to:{transform:"scaleY(1)",transformOrigin:N},begin:v,easing:y,isActive:f,duration:d,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:N}},n.createElement("line",p({},e,{style:r})))}))});return n.createElement(a.m,{className:"recharts-errorBars"},j)}var d=(0,n.createContext)(void 0);function y(e){var{direction:r,children:t}=e;return n.createElement(d.Provider,{value:r},t)}var m={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function b(e){var r,t,a=(r=e.direction,t=(0,n.useContext)(d),null!=r?r:null!=t?t:"x"),{width:i,isAnimationActive:l,animationBegin:c,animationDuration:s,animationEasing:f}=(0,u.j)(e,m);return n.createElement(n.Fragment,null,n.createElement(o.Jm,{dataKey:e.dataKey,direction:a}),n.createElement(v,p({},e,{direction:a,width:i,isAnimationActive:l,animationBegin:c,animationDuration:s,animationEasing:f})))}class h extends n.Component{render(){return n.createElement(b,this.props)}}f(h,"defaultProps",m),f(h,"displayName","ErrorBar")},35450:function(e,r,t){t.d(r,{Y:function(){return o}});var n=t(2265),a=t(39040),i=t(19579),o=()=>{var e=(0,a.T)();return(0,n.useEffect)(()=>(e((0,i.a1)()),()=>{e((0,i.nF)())})),null}},9145:function(e,r,t){t.d(r,{Ay:function(){return P},BU:function(){return j},fu:function(){return g},iR:function(){return S}});var n=t(92713),a=t(98628),i=t(16630),o=t(49037),l=t(17008),u=t(35953),c=t(22932),s=t(74653),f=t(33968),p=t(66395);function v(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function d(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?v(Object(t),!0).forEach(function(r){var n,a;n=r,a=t[r],(n=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(n))in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):v(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var y=(e,r,t,n,a)=>a,m=(e,r,t)=>{var n=null!=t?t:e;if(!(0,i.Rw)(n))return(0,i.h1)(n,r,0)},b=(0,n.P1)([u.rE,a.bm,(e,r)=>r,(e,r,t)=>t,(e,r,t,n)=>n],(e,r,t,n,a)=>r.filter(r=>"horizontal"===e?r.xAxisId===t:r.yAxisId===n).filter(e=>e.isPanorama===a).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function h(e){return null!=e.stackId&&null!=e.dataKey}var g=(e,r,t)=>{var n=e.filter(h),a=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,r)=>(e[r.stackId]||(e[r.stackId]=[]),e[r.stackId].push(r),e),{})).map(e=>{var[n,a]=e;return{stackId:n,dataKeys:a.map(e=>e.dataKey),barSize:m(r,t,a[0].barSize)}}),...a.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:m(r,t,e.barSize)}))]},x=(0,n.P1)([b,f.X8,(e,r,t)=>"horizontal"===(0,u.rE)(e)?(0,a.Lu)(e,"xAxis",r):(0,a.Lu)(e,"yAxis",t)],g),O=(e,r,t,n)=>{var i,l;return"horizontal"===(0,u.rE)(e)?(i=(0,a.AS)(e,"xAxis",r,n),l=(0,a.bY)(e,"xAxis",r,n)):(i=(0,a.AS)(e,"yAxis",t,n),l=(0,a.bY)(e,"yAxis",t,n)),(0,o.zT)(i,l)},P=(e,r,t,n,a,o,l)=>{var u=function(e,r,t,n,a){var o,l=n.length;if(!(l<1)){var u=(0,i.h1)(e,t,0,!0),c=[];if((0,p.n)(n[0].barSize)){var s=!1,f=t/l,v=n.reduce((e,r)=>e+(r.barSize||0),0);(v+=(l-1)*u)>=t&&(v-=(l-1)*u,u=0),v>=t&&f>0&&(s=!0,f*=.9,v=l*f);var d={offset:((t-v)/2>>0)-u,size:0};o=n.reduce((e,r)=>{var t,n=[...e,{stackId:r.stackId,dataKeys:r.dataKeys,position:{offset:d.offset+d.size+u,size:s?f:null!==(t=r.barSize)&&void 0!==t?t:0}}];return d=n[n.length-1].position,n},c)}else{var y=(0,i.h1)(r,t,0,!0);t-2*y-(l-1)*u<=0&&(u=0);var m=(t-2*y-(l-1)*u)/l;m>1&&(m>>=0);var b=(0,p.n)(a)?Math.min(m,a):m;o=n.reduce((e,r,t)=>[...e,{stackId:r.stackId,dataKeys:r.dataKeys,position:{offset:y+(m+u)*t+(m-b)/2,size:b}}],c)}return o}}(t,n,a!==o?a:o,e,(0,i.Rw)(l)?r:l);return a!==o&&null!=u&&(u=u.map(e=>d(d({},e),{},{position:d(d({},e.position),{},{offset:e.position.offset-a/2})}))),u},E=(0,n.P1)([x,f.qy,f.wK,f.sd,(e,r,t,n,l)=>{var c,s,p,v,d=(0,u.rE)(e),y=(0,f.qy)(e),{maxBarSize:m}=l,b=(0,i.Rw)(m)?y:m;return"horizontal"===d?(p=(0,a.AS)(e,"xAxis",r,n),v=(0,a.bY)(e,"xAxis",r,n)):(p=(0,a.AS)(e,"yAxis",t,n),v=(0,a.bY)(e,"yAxis",t,n)),null!==(c=null!==(s=(0,o.zT)(p,v,!0))&&void 0!==s?s:b)&&void 0!==c?c:0},O,(e,r,t,n,a)=>a.maxBarSize],P),w=(0,n.P1)([E,y],(e,r)=>{if(null!=e){var t=e.find(e=>e.stackId===r.stackId&&e.dataKeys.includes(r.dataKey));if(null!=t)return t.position}}),j=(e,r)=>{if(e&&(null==r?void 0:r.dataKey)!=null){var{stackId:t}=r;if(null!=t){var n=e[t];if(n){var{stackedData:a}=n;if(a)return a.find(e=>e.key===r.dataKey)}}}},A=(0,n.P1)([a.bm,y],(e,r)=>{if(e.some(e=>"bar"===e.type&&r.dataKey===e.dataKey&&r.stackId===e.stackId&&r.stackId===e.stackId))return r}),k=(0,n.P1)([(e,r,t,n)=>"horizontal"===(0,u.rE)(e)?(0,a.g6)(e,"yAxis",t,n):(0,a.g6)(e,"xAxis",r,n),y],j),S=(0,n.P1)([s.DX,(e,r,t,n)=>(0,a.AS)(e,"xAxis",r,n),(e,r,t,n)=>(0,a.AS)(e,"yAxis",t,n),(e,r,t,n)=>(0,a.bY)(e,"xAxis",r,n),(e,r,t,n)=>(0,a.bY)(e,"yAxis",t,n),w,u.rE,c.hA,O,k,A,(e,r,t,n,a,i)=>i],(e,r,t,n,a,i,o,u,c,s,f,p)=>{var v,{chartData:d,dataStartIndex:y,dataEndIndex:m}=u;if(null!=f&&null!=i&&("horizontal"===o||"vertical"===o)&&null!=r&&null!=t&&null!=n&&null!=a&&null!=c){var{data:b}=f;if(null!=(v=null!=b&&b.length>0?b:null==d?void 0:d.slice(y,m+1)))return(0,l.u)({layout:o,barSettings:f,pos:i,bandSize:c,xAxis:r,yAxis:t,xAxisTicks:n,yAxisTicks:a,stackedData:s,displayedData:v,offset:e,cells:p})}})}}]);