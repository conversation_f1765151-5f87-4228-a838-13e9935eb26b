{"/account-status/page": "app/account-status/page.js", "/_not-found/page": "app/_not-found/page.js", "/auctions/[id]/page": "app/auctions/[id]/page.js", "/auth/forgot-password/page": "app/auth/forgot-password/page.js", "/company/auctions/page": "app/company/auctions/page.js", "/auth/verify-email/page": "app/auth/verify-email/page.js", "/auth/register/page": "app/auth/register/page.js", "/auctions/[id]/edit/page": "app/auctions/[id]/edit/page.js", "/company/create-auction/page": "app/company/create-auction/page.js", "/company/notifications/page": "app/company/notifications/page.js", "/auth/login/page": "app/auth/login/page.js", "/company/profile/page": "app/company/profile/page.js", "/favorites/page": "app/favorites/page.js", "/company/bids/page": "app/company/bids/page.js", "/government/applications/page": "app/government/applications/page.js", "/government/applications/[id]/page": "app/government/applications/[id]/page.js", "/government/create-tender/page": "app/government/create-tender/page.js", "/government/notifications/page": "app/government/notifications/page.js", "/government/profile/page": "app/government/profile/page.js", "/company/dashboard/page": "app/company/dashboard/page.js", "/government/dashboard/page": "app/government/dashboard/page.js", "/government/tenders/page": "app/government/tenders/page.js", "/notifications/page": "app/notifications/page.js", "/page": "app/page.js", "/settings/page": "app/settings/page.js", "/tenders/[id]/edit/page": "app/tenders/[id]/edit/page.js", "/support/page": "app/support/page.js", "/dashboard/page": "app/dashboard/page.js", "/search/page": "app/search/page.js", "/tenders/page": "app/tenders/page.js", "/user/auctions/page": "app/user/auctions/page.js", "/user/applications/page": "app/user/applications/page.js", "/user/dashboard/page": "app/user/dashboard/page.js", "/user/notifications/page": "app/user/notifications/page.js", "/user/leaderboard/page": "app/user/leaderboard/page.js", "/user/bids/page": "app/user/bids/page.js", "/upload-documents/page": "app/upload-documents/page.js", "/user/my-bids/page": "app/user/my-bids/page.js", "/user/profile/customization/page": "app/user/profile/customization/page.js", "/user/profile/page": "app/user/profile/page.js", "/tenders/[id]/page": "app/tenders/[id]/page.js", "/user/tenders/page": "app/user/tenders/page.js", "/admin/auctions/[id]/page": "app/admin/auctions/[id]/page.js", "/admin/pending-accounts/page": "app/admin/pending-accounts/page.js", "/admin/create-tender/page": "app/admin/create-tender/page.js", "/admin/dashboard/page": "app/admin/dashboard/page.js", "/admin/email-templates/page": "app/admin/email-templates/page.js", "/admin/auctions/page": "app/admin/auctions/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/auctions/[id]/edit/page": "app/admin/auctions/[id]/edit/page.js", "/admin/tenders/[id]/edit/page": "app/admin/tenders/[id]/edit/page.js", "/admin/users/[id]/page": "app/admin/users/[id]/page.js", "/admin/tenders/page": "app/admin/tenders/page.js", "/admin/reports/page": "app/admin/reports/page.js", "/admin/tenders/[id]/page": "app/admin/tenders/[id]/page.js"}