"use strict";exports.id=9642,exports.ids=[9642],exports.modules={82561:(e,t,r)=>{r.d(t,{M:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},7747:(e,t,r)=>{r.d(t,{B:()=>c});var n=r(17577),o=r(93095),l=r(48051),u=r(34214),i=r(10326);function c(e){let t=e+"CollectionProvider",[r,c]=(0,o.b)(t),[f,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,o=n.useRef(null),l=n.useRef(new Map).current;return(0,i.jsx)(f,{scope:t,itemMap:l,collectionRef:o,children:r})};a.displayName=t;let d=e+"CollectionSlot",m=(0,u.Z8)(d),p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=s(d,r),u=(0,l.e)(t,o.collectionRef);return(0,i.jsx)(m,{ref:u,children:n})});p.displayName=d;let v=e+"CollectionItemSlot",x="data-radix-collection-item",R=(0,u.Z8)(v),S=n.forwardRef((e,t)=>{let{scope:r,children:o,...u}=e,c=n.useRef(null),f=(0,l.e)(t,c),a=s(v,r);return n.useEffect(()=>(a.itemMap.set(c,{ref:c,...u}),()=>void a.itemMap.delete(c))),(0,i.jsx)(R,{[x]:"",ref:f,children:o})});return S.displayName=v,[{Provider:a,Slot:p,ItemSlot:S},function(t){let r=s(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${x}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},c]}},93095:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(17577),o=r(10326);function l(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return l.scopeName=e,[function(t,l){let u=n.createContext(l),i=r.length;r=[...r,l];let c=t=>{let{scope:r,children:l,...c}=t,f=r?.[e]?.[i]||u,s=n.useMemo(()=>c,Object.values(c));return(0,o.jsx)(f.Provider,{value:s,children:l})};return c.displayName=t+"Provider",[c,function(r,o){let c=o?.[e]?.[i]||u,f=n.useContext(c);if(f)return f;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(l,...t)]}},17124:(e,t,r)=>{r.d(t,{gm:()=>l});var n=r(17577);r(10326);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}},88957:(e,t,r)=>{r.d(t,{M:()=>c});var n,o=r(17577),l=r(65819),u=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function c(e){let[t,r]=o.useState(u());return(0,l.b)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},45226:(e,t,r)=>{r.d(t,{WV:()=>i,jH:()=>c});var n=r(17577),o=r(60962),l=r(34214),u=r(10326),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e,i=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},55049:(e,t,r)=>{r.d(t,{W:()=>o});var n=r(17577);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},52067:(e,t,r)=>{r.d(t,{T:()=>i});var n,o=r(17577),l=r(65819),u=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.b;function i({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,i,c]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),i=o.useRef(t);return u(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==r&&(i.current?.(r),l.current=r)},[r,l]),[r,n,i]}({defaultProp:t,onChange:r}),f=void 0!==e,s=f?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==f){let t=f?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=f},[f,n])}return[s,o.useCallback(t=>{if(f){let r="function"==typeof t?t(e):t;r!==e&&c.current?.(r)}else i(t)},[f,e,i,c])]}Symbol("RADIX:SYNC_STATE")},65819:(e,t,r)=>{r.d(t,{b:()=>o});var n=r(17577),o=globalThis?.document?n.useLayoutEffect:()=>{}}};