"use strict";exports.id=7017,exports.ids=[7017],exports.modules={32933:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},941:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},96633:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(76557).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},13048:(e,t,n)=>{n.d(t,{VY:()=>rc,ZA:()=>rd,JO:()=>ra,ck:()=>rp,wU:()=>rm,eT:()=>rh,__:()=>rf,h_:()=>ru,fC:()=>ro,$G:()=>rg,u_:()=>rv,Z0:()=>ry,xz:()=>ri,B4:()=>rl,l_:()=>rs});var r,o,i,l=n(17577),a=n(60962);function u(e,[t,n]){return Math.min(n,Math.max(t,e))}var c=n(82561),s=n(7747),d=n(48051),f=n(93095),p=n(17124),h=n(45226),m=n(55049),v=n(10326),g="dismissableLayer.update",y=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:u,onDismiss:s,...f}=e,p=l.useContext(y),[w,E]=l.useState(null),S=w?.ownerDocument??globalThis?.document,[,C]=l.useState({}),R=(0,d.e)(t,e=>E(e)),T=Array.from(p.layers),[L]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),A=T.indexOf(L),P=w?T.indexOf(w):-1,k=p.layersWithOutsidePointerEventsDisabled.size>0,M=P>=A,j=function(e,t=globalThis?.document){let n=(0,m.W)(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){x("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...p.branches].some(e=>e.contains(t));!M||n||(i?.(e),u?.(e),e.defaultPrevented||s?.())},S),D=function(e,t=globalThis?.document){let n=(0,m.W)(e),r=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!r.current&&x("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...p.branches].some(e=>e.contains(t))||(a?.(e),u?.(e),e.defaultPrevented||s?.())},S);return function(e,t=globalThis?.document){let n=(0,m.W)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P!==p.layers.size-1||(r?.(e),!e.defaultPrevented&&s&&(e.preventDefault(),s()))},S),l.useEffect(()=>{if(w)return n&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(o=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(w)),p.layers.add(w),b(),()=>{n&&1===p.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=o)}},[w,S,n,p]),l.useEffect(()=>()=>{w&&(p.layers.delete(w),p.layersWithOutsidePointerEventsDisabled.delete(w),b())},[w,p]),l.useEffect(()=>{let e=()=>C({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,v.jsx)(h.WV.div,{...f,ref:R,style:{pointerEvents:k?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,c.M)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,c.M)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,c.M)(e.onPointerDownCapture,j.onPointerDownCapture)})});function b(){let e=new CustomEvent(g);document.dispatchEvent(e)}function x(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,h.jH)(o,i):o.dispatchEvent(i)}w.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(y),r=l.useRef(null),o=(0,d.e)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(h.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var E=0;function S(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var C="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",T={bubbles:!1,cancelable:!0},L=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[u,c]=l.useState(null),s=(0,m.W)(o),f=(0,m.W)(i),p=l.useRef(null),g=(0,d.e)(t,e=>c(e)),y=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(y.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:k(p.current,{select:!0})},t=function(e){if(y.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||k(p.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&k(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,y.paused]),l.useEffect(()=>{if(u){M.add(y);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(C,T);u.addEventListener(C,s),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(k(r,{select:t}),document.activeElement!==n)return}(A(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&k(u))}return()=>{u.removeEventListener(C,s),setTimeout(()=>{let t=new CustomEvent(R,T);u.addEventListener(R,f),u.dispatchEvent(t),t.defaultPrevented||k(e??document.body,{select:!0}),u.removeEventListener(R,f),M.remove(y)},0)}}},[u,s,f,y]);let w=l.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=A(e);return[P(t,e),P(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&k(i,{select:!0})):(e.preventDefault(),n&&k(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,v.jsx)(h.WV.div,{tabIndex:-1,...a,ref:g,onKeyDown:w})});function A(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function P(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function k(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}L.displayName="FocusScope";var M=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=j(e,t)).unshift(t)},remove(t){e=j(e,t),e[0]?.resume()}}}();function j(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var D=n(88957);let N=["top","right","bottom","left"],O=Math.min,W=Math.max,I=Math.round,H=Math.floor,F=e=>({x:e,y:e}),V={left:"right",right:"left",bottom:"top",top:"bottom"},B={start:"end",end:"start"};function _(e,t){return"function"==typeof e?e(t):e}function z(e){return e.split("-")[0]}function K(e){return e.split("-")[1]}function $(e){return"x"===e?"y":"x"}function Y(e){return"y"===e?"height":"width"}let U=new Set(["top","bottom"]);function Z(e){return U.has(z(e))?"y":"x"}function X(e){return e.replace(/start|end/g,e=>B[e])}let q=["left","right"],G=["right","left"],J=["top","bottom"],Q=["bottom","top"];function ee(e){return e.replace(/left|right|bottom|top/g,e=>V[e])}function et(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function en(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function er(e,t,n){let r,{reference:o,floating:i}=e,l=Z(t),a=$(Z(t)),u=Y(a),c=z(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(K(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let eo=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=er(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=er(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function ei(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=_(t,e),h=et(p),m=a[f?"floating"===d?"reference":"floating":d],v=en(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=en(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-b.top+h.top)/w.y,bottom:(b.bottom-v.bottom+h.bottom)/w.y,left:(v.left-b.left+h.left)/w.x,right:(b.right-v.right+h.right)/w.x}}function el(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ea(e){return N.some(t=>e[t]>=0)}let eu=new Set(["left","top"]);async function ec(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=z(n),a=K(n),u="y"===Z(n),c=eu.has(l)?-1:1,s=i&&u?-1:1,d=_(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function es(){return"undefined"!=typeof window}function ed(e){return eh(e)?(e.nodeName||"").toLowerCase():"#document"}function ef(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ep(e){var t;return null==(t=(eh(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eh(e){return!!es()&&(e instanceof Node||e instanceof ef(e).Node)}function em(e){return!!es()&&(e instanceof Element||e instanceof ef(e).Element)}function ev(e){return!!es()&&(e instanceof HTMLElement||e instanceof ef(e).HTMLElement)}function eg(e){return!!es()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ef(e).ShadowRoot)}let ey=new Set(["inline","contents"]);function ew(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ek(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ey.has(o)}let eb=new Set(["table","td","th"]),ex=[":popover-open",":modal"];function eE(e){return ex.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eS=["transform","translate","scale","rotate","perspective"],eC=["transform","translate","scale","rotate","perspective","filter"],eR=["paint","layout","strict","content"];function eT(e){let t=eL(),n=em(e)?ek(e):e;return eS.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eC.some(e=>(n.willChange||"").includes(e))||eR.some(e=>(n.contain||"").includes(e))}function eL(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eA=new Set(["html","body","#document"]);function eP(e){return eA.has(ed(e))}function ek(e){return ef(e).getComputedStyle(e)}function eM(e){return em(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ej(e){if("html"===ed(e))return e;let t=e.assignedSlot||e.parentNode||eg(e)&&e.host||ep(e);return eg(t)?t.host:t}function eD(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ej(t);return eP(n)?t.ownerDocument?t.ownerDocument.body:t.body:ev(n)&&ew(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=ef(o);if(i){let e=eN(l);return t.concat(l,l.visualViewport||[],ew(o)?o:[],e&&n?eD(e):[])}return t.concat(o,eD(o,[],n))}function eN(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eO(e){let t=ek(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ev(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=I(n)!==i||I(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eW(e){return em(e)?e:e.contextElement}function eI(e){let t=eW(e);if(!ev(t))return F(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eO(t),l=(i?I(n.width):n.width)/r,a=(i?I(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eH=F(0);function eF(e){let t=ef(e);return eL()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eH}function eV(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eW(e),a=F(1);t&&(r?em(r)&&(a=eI(r)):a=eI(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ef(l))&&o)?eF(l):F(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=ef(l),t=r&&em(r)?ef(r):r,n=e,o=eN(n);for(;o&&r&&t!==n;){let e=eI(o),t=o.getBoundingClientRect(),r=ek(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eN(n=ef(o))}}return en({width:d,height:f,x:c,y:s})}function eB(e,t){let n=eM(e).scrollLeft;return t?t.left+n:eV(ep(e)).left+n}function e_(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eB(e,r)),y:r.top+t.scrollTop}}let ez=new Set(["absolute","fixed"]);function eK(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ef(e),r=ep(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eL();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=ep(e),n=eM(e),r=e.ownerDocument.body,o=W(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=W(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eB(e),a=-n.scrollTop;return"rtl"===ek(r).direction&&(l+=W(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(ep(e));else if(em(t))r=function(e,t){let n=eV(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ev(e)?eI(e):F(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eF(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return en(r)}function e$(e){return"static"===ek(e).position}function eY(e,t){if(!ev(e)||"fixed"===ek(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ep(e)===n&&(n=n.ownerDocument.body),n}function eU(e,t){var n;let r=ef(e);if(eE(e))return r;if(!ev(e)){let t=ej(e);for(;t&&!eP(t);){if(em(t)&&!e$(t))return t;t=ej(t)}return r}let o=eY(e,t);for(;o&&(n=o,eb.has(ed(n)))&&e$(o);)o=eY(o,t);return o&&eP(o)&&e$(o)&&!eT(o)?r:o||function(e){let t=ej(e);for(;ev(t)&&!eP(t);){if(eT(t))return t;if(eE(t))break;t=ej(t)}return null}(e)||r}let eZ=async function(e){let t=this.getOffsetParent||eU,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ev(t),o=ep(t),i="fixed"===n,l=eV(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=F(0);if(r||!r&&!i){if(("body"!==ed(t)||ew(o))&&(a=eM(t)),r){let e=eV(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eB(o))}i&&!r&&o&&(u.x=eB(o));let c=!o||r||i?F(0):e_(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eX={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=ep(r),a=!!t&&eE(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=F(1),s=F(0),d=ev(r);if((d||!d&&!i)&&(("body"!==ed(r)||ew(l))&&(u=eM(r)),ev(r))){let e=eV(r);c=eI(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?F(0):e_(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:ep,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eE(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eD(e,[],!1).filter(e=>em(e)&&"body"!==ed(e)),o=null,i="fixed"===ek(e).position,l=i?ej(e):e;for(;em(l)&&!eP(l);){let t=ek(l),n=eT(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ez.has(o.position)||ew(l)&&!n&&function e(t,n){let r=ej(t);return!(r===n||!em(r)||eP(r))&&("fixed"===ek(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ej(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eK(t,n,o);return e.top=W(r.top,e.top),e.right=O(r.right,e.right),e.bottom=O(r.bottom,e.bottom),e.left=W(r.left,e.left),e},eK(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eU,getElementRects:eZ,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eO(e);return{width:t,height:n}},getScale:eI,isElement:em,isRTL:function(e){return"rtl"===ek(e).direction}};function eq(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eG=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=_(e,t)||{};if(null==c)return{};let d=et(s),f={x:n,y:r},p=$(Z(o)),h=Y(p),m=await l.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),x=b?b[g]:0;x&&await (null==l.isElement?void 0:l.isElement(b))||(x=a.floating[g]||i.floating[h]);let E=x/2-m[h]/2-1,S=O(d[v?"top":"left"],E),C=O(d[v?"bottom":"right"],E),R=x-m[h]-C,T=x/2-m[h]/2+(y/2-w/2),L=W(S,O(T,R)),A=!u.arrow&&null!=K(o)&&T!==L&&i.reference[h]/2-(T<S?S:C)-m[h]/2<0,P=A?T<S?T-S:T-R:0;return{[p]:f[p]+P,data:{[p]:L,centerOffset:T-L-P,...A&&{alignmentOffset:P}},reset:A}}}),eJ=(e,t,n)=>{let r=new Map,o={platform:eX,...n},i={...o.platform,_c:r};return eo(e,t,{...o,platform:i})};var eQ="undefined"!=typeof document?l.useLayoutEffect:function(){};function e0(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e0(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e0(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e1(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e2(e,t){let n=e1(e);return Math.round(t*n)/n}function e5(e){let t=l.useRef(e);return eQ(()=>{t.current=e}),t}let e6=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eG({element:n.current,padding:r}).fn(t):{}:n?eG({element:n,padding:r}).fn(t):{}}}),e7=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await ec(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),e3=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=_(e,t),c={x:n,y:r},s=await ei(t,u),d=Z(z(o)),f=$(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=W(n,O(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=W(n,O(h,r))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),e9=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=_(e,t),s={x:n,y:r},d=Z(o),f=$(d),p=s[f],h=s[d],m=_(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=eu.has(z(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),e4=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=_(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=z(a),x=Z(s),E=z(s)===s,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=m||(E||!y?[ee(s)]:function(e){let t=ee(e);return[X(e),t,X(t)]}(s)),R="none"!==g;!m&&R&&C.push(...function(e,t,n,r){let o=K(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?G:q;return t?q:G;case"left":case"right":return t?J:Q;default:return[]}}(z(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(X)))),i}(s,y,g,S));let T=[s,...C],L=await ei(t,w),A=[],P=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&A.push(L[b]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=K(e),o=$(Z(e)),i=Y(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ee(l)),[l,ee(l)]}(a,c,S);A.push(L[e[0]],L[e[1]])}if(P=[...P,{placement:a,overflows:A}],!A.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=T[e];if(t&&(!("alignment"===h&&x!==Z(t))||P.every(e=>e.overflows[0]>0&&Z(e.placement)===x)))return{data:{index:e,overflows:P},reset:{placement:t}};let n=null==(i=P.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=P.filter(e=>{if(R){let t=Z(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),e8=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=_(e,t),f=await ei(t,d),p=z(l),h=K(l),m="y"===Z(l),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=v-f.left-f.right,b=O(g-f[o],y),x=O(v-f[i],w),E=!t.middlewareData.shift,S=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!h){let e=W(f.left,0),t=W(f.right,0),n=W(f.top,0),r=W(f.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:W(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:W(f.top,f.bottom))}await s({...t,availableWidth:C,availableHeight:S});let R=await u.getDimensions(c.floating);return v!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),te=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=_(e,t);switch(r){case"referenceHidden":{let e=el(await ei(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ea(e)}}}case"escaped":{let e=el(await ei(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ea(e)}}}default:return{}}}}}(e),options:[e,t]}),tt=(e,t)=>({...e6(e),options:[e,t]});var tn=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,v.jsx)(h.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tn.displayName="Arrow";var tr=n(65819),to=n(2566),ti="Popper",[tl,ta]=(0,f.b)(ti),[tu,tc]=tl(ti),ts=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,v.jsx)(tu,{scope:t,anchor:r,onAnchorChange:o,children:n})};ts.displayName=ti;var td="PopperAnchor",tf=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=tc(td,n),a=l.useRef(null),u=(0,d.e)(t,a);return l.useEffect(()=>{i.onAnchorChange(r?.current||a.current)}),r?null:(0,v.jsx)(h.WV.div,{...o,ref:u})});tf.displayName=td;var tp="PopperContent",[th,tm]=tl(tp),tv=l.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:s=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:g="partial",hideWhenDetached:y=!1,updatePositionStrategy:w="optimized",onPlaced:b,...x}=e,E=tc(tp,n),[S,C]=l.useState(null),R=(0,d.e)(t,e=>C(e)),[T,L]=l.useState(null),A=(0,to.t)(T),P=A?.width??0,k=A?.height??0,M="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},j=Array.isArray(f)?f:[f],D=j.length>0,N={padding:M,boundary:j.filter(tb),altBoundary:D},{refs:I,floatingStyles:F,placement:V,isPositioned:B,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=l.useState(r);e0(h,r)||m(r);let[v,g]=l.useState(null),[y,w]=l.useState(null),b=l.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=l.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||v,S=u||y,C=l.useRef(null),R=l.useRef(null),T=l.useRef(f),L=null!=s,A=e5(s),P=e5(o),k=e5(d),M=l.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};P.current&&(e.platform=P.current),eJ(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};j.current&&!e0(T.current,t)&&(T.current=t,a.flushSync(()=>{p(t)}))})},[h,t,n,P,k]);eQ(()=>{!1===d&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let j=l.useRef(!1);eQ(()=>(j.current=!0,()=>{j.current=!1}),[]),eQ(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(A.current)return A.current(E,S,M);M()}},[E,S,M,A,L]);let D=l.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),N=l.useMemo(()=>({reference:E,floating:S}),[E,S]),O=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=e2(N.floating,f.x),r=e2(N.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...e1(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,N.floating,f.x,f.y]);return l.useMemo(()=>({...f,update:M,refs:D,elements:N,floatingStyles:O}),[f,M,D,N,O])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eW(e),d=i||l?[...s?eD(s):[],...eD(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=ep(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=H(d),m=H(o.clientWidth-(s+f)),v={rootMargin:-h+"px "+-m+"px "+-H(o.clientHeight-(d+p))+"px "+-H(s)+"px",threshold:W(0,O(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eq(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?eV(e):null;return c&&function t(){let r=eV(e);m&&!eq(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===w}),elements:{reference:E.anchor},middleware:[e7({mainAxis:o+k,alignmentAxis:u}),s&&e3({mainAxis:!0,crossAxis:!1,limiter:"partial"===g?e9():void 0,...N}),s&&e4({...N}),e8({...N,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),T&&tt({element:T,padding:c}),tx({arrowWidth:P,arrowHeight:k}),y&&te({strategy:"referenceHidden",...N})]}),[z,K]=tE(V),$=(0,m.W)(b);(0,tr.b)(()=>{B&&$?.()},[B,$]);let Y=_.arrow?.x,U=_.arrow?.y,Z=_.arrow?.centerOffset!==0,[X,q]=l.useState();return(0,tr.b)(()=>{S&&q(window.getComputedStyle(S).zIndex)},[S]),(0,v.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:B?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(th,{scope:n,placedSide:z,onArrowChange:L,arrowX:Y,arrowY:U,shouldHideArrow:Z,children:(0,v.jsx)(h.WV.div,{"data-side":z,"data-align":K,...x,ref:R,style:{...x.style,animation:B?void 0:"none"}})})})});tv.displayName=tp;var tg="PopperArrow",ty={top:"bottom",right:"left",bottom:"top",left:"right"},tw=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tm(tg,n),i=ty[o.placedSide];return(0,v.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(tn,{...r,ref:t,style:{...r.style,display:"block"}})})});function tb(e){return null!==e}tw.displayName=tg;var tx=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,c]=tE(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===u?(p=i?s:`${d}px`,h=`${-a}px`):"top"===u?(p=i?s:`${d}px`,h=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,h=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+a}px`,h=i?s:`${f}px`),{data:{x:p,y:h}}}});function tE(e){let[t,n="center"]=e.split("-");return[t,n]}var tS=l.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=l.useState(!1);(0,tr.b)(()=>i(!0),[]);let u=n||o&&globalThis?.document?.body;return u?a.createPortal((0,v.jsx)(h.WV.div,{...r,ref:t}),u):null});tS.displayName="Portal";var tC=n(34214),tR=n(52067),tT=n(53405),tL=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,v.jsx)(h.WV.span,{...e,ref:t,style:{...tL,...e.style}})).displayName="VisuallyHidden";var tA=new WeakMap,tP=new WeakMap,tk={},tM=0,tj=function(e){return e&&(e.host||tj(e.parentNode))},tD=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tj(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tk[n]||(tk[n]=new WeakMap);var i=tk[n],l=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tA.get(e)||0)+1,c=(i.get(e)||0)+1;tA.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tP.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tM++,function(){l.forEach(function(e){var t=tA.get(e)-1,o=i.get(e)-1;tA.set(e,t),i.set(e,o),t||(tP.has(e)||e.removeAttribute(r),tP.delete(e)),o||e.removeAttribute(n)}),--tM||(tA=new WeakMap,tA=new WeakMap,tP=new WeakMap,tk={})}},tN=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),i=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),tD(o,i,n,"aria-hidden")):function(){return null}},tO=function(){return(tO=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tW(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var tI=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tH="width-before-scroll-bar";function tF(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tV="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,tB=new WeakMap;function t_(e){return e}var tz=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=t_),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=tO({async:!0,ssr:!1},e),o}(),tK=function(){},t$=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),u=l.useState({onScrollCapture:tK,onWheelCapture:tK,onTouchMoveCapture:tK}),c=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,E=e.as,S=e.gapMode,C=tW(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[a,t],r=function(e){return n.forEach(function(t){return tF(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tV(function(){var e=tB.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tF(e,null)}),r.forEach(function(e){t.has(e)||tF(e,o)})}tB.set(i,n)},[n]),i),T=tO(tO({},C),c);return l.createElement(l.Fragment,null,m&&l.createElement(g,{sideCar:tz,removeScrollBar:h,shards:v,noRelative:y,noIsolation:w,inert:b,setCallbacks:s,allowPinchZoom:!!x,lockRef:a,gapMode:S}),d?l.cloneElement(l.Children.only(f),tO(tO({},T),{ref:R})):l.createElement(void 0===E?"div":E,tO({},T,{className:p,ref:R}),f))});t$.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t$.classNames={fullWidth:tH,zeroRight:tI};var tY=function(e){var t=e.sideCar,n=tW(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,tO({},n))};tY.isSideCarExport=!0;var tU=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tZ=function(){var e=tU();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tX=function(){var e=tZ();return function(t){return e(t.styles,t.dynamic),null}},tq={left:0,top:0,right:0,gap:0},tG=function(e){return parseInt(e||"",10)||0},tJ=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tG(n),tG(r),tG(o)]},tQ=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tq;var t=tJ(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t0=tX(),t1="data-scroll-locked",t2=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(t1,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tI," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tH," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tI," .").concat(tI," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tH," .").concat(tH," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t1,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},t5=function(){var e=parseInt(document.body.getAttribute(t1)||"0",10);return isFinite(e)?e:0},t6=function(){l.useEffect(function(){return document.body.setAttribute(t1,(t5()+1).toString()),function(){var e=t5()-1;e<=0?document.body.removeAttribute(t1):document.body.setAttribute(t1,e.toString())}},[])},t7=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t6();var i=l.useMemo(function(){return tQ(o)},[o]);return l.createElement(t0,{styles:t2(i,!t,o,n?"":"!important")})},t3=!1;if("undefined"!=typeof window)try{var t9=Object.defineProperty({},"passive",{get:function(){return t3=!0,!0}});window.addEventListener("test",t9,t9),window.removeEventListener("test",t9,t9)}catch(e){t3=!1}var t4=!!t3&&{passive:!1},t8=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},ne=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nt(e,r)){var o=nn(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nt=function(e,t){return"v"===e?t8(t,"overflowY"):t8(t,"overflowX")},nn=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nr=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=nn(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&nt(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},no=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ni=function(e){return[e.deltaX,e.deltaY]},nl=function(e){return e&&"current"in e?e.current:e},na=0,nu=[];let nc=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(na++)[0],i=l.useState(tX)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nl),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=no(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=ne(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ne(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nr(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(nu.length&&nu[nu.length-1]===i){var n="deltaY"in e?ni(e):no(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(nl).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=no(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,ni(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,no(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return nu.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,t4),document.addEventListener("touchmove",c,t4),document.addEventListener("touchstart",d,t4),function(){nu=nu.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,t4),document.removeEventListener("touchmove",c,t4),document.removeEventListener("touchstart",d,t4)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(t7,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tz.useMedium(r),tY);var ns=l.forwardRef(function(e,t){return l.createElement(t$,tO({},e,{ref:t,sideCar:nc}))});ns.classNames=t$.classNames;var nd=[" ","Enter","ArrowUp","ArrowDown"],nf=[" ","Enter"],np="Select",[nh,nm,nv]=(0,s.B)(np),[ng,ny]=(0,f.b)(np,[nv,ta]),nw=ta(),[nb,nx]=ng(np),[nE,nS]=ng(np),nC=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:a,defaultValue:u,onValueChange:c,dir:s,name:d,autoComplete:f,disabled:h,required:m,form:g}=e,y=nw(t),[w,b]=l.useState(null),[x,E]=l.useState(null),[S,C]=l.useState(!1),R=(0,p.gm)(s),[T,L]=(0,tR.T)({prop:r,defaultProp:o??!1,onChange:i,caller:np}),[A,P]=(0,tR.T)({prop:a,defaultProp:u,onChange:c,caller:np}),k=l.useRef(null),M=!w||g||!!w.closest("form"),[j,N]=l.useState(new Set),O=Array.from(j).map(e=>e.props.value).join(";");return(0,v.jsx)(ts,{...y,children:(0,v.jsxs)(nb,{required:m,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,D.M)(),value:A,onValueChange:P,open:T,onOpenChange:L,dir:R,triggerPointerDownPosRef:k,disabled:h,children:[(0,v.jsx)(nh.Provider,{scope:t,children:(0,v.jsx)(nE,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{N(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{N(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,v.jsxs)(re,{"aria-hidden":!0,required:m,tabIndex:-1,name:d,autoComplete:f,value:A,onChange:e=>P(e.target.value),disabled:h,form:g,children:[void 0===A?(0,v.jsx)("option",{value:""}):null,Array.from(j)]},O):null]})})};nC.displayName=np;var nR="SelectTrigger",nT=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nw(n),a=nx(nR,n),u=a.disabled||r,s=(0,d.e)(t,a.onTriggerChange),f=nm(n),p=l.useRef("touch"),[m,g,y]=rn(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=rr(t,e,n);void 0!==r&&a.onValueChange(r.value)}),w=e=>{u||(a.onOpenChange(!0),y()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,v.jsx)(tf,{asChild:!0,...i,children:(0,v.jsx)(h.WV.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":rt(a.value)?"":void 0,...o,ref:s,onClick:(0,c.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,c.M)(o.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,c.M)(o.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&nd.includes(e.key)&&(w(),e.preventDefault())})})})});nT.displayName=nR;var nL="SelectValue",nA=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nx(nL,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,f=(0,d.e)(t,u.onValueNodeChange);return(0,tr.b)(()=>{c(s)},[c,s]),(0,v.jsx)(h.WV.span,{...a,ref:f,style:{pointerEvents:"none"},children:rt(u.value)?(0,v.jsx)(v.Fragment,{children:l}):i})});nA.displayName=nL;var nP=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,v.jsx)(h.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nP.displayName="SelectIcon";var nk=e=>(0,v.jsx)(tS,{asChild:!0,...e});nk.displayName="SelectPortal";var nM="SelectContent",nj=l.forwardRef((e,t)=>{let n=nx(nM,e.__scopeSelect),[r,o]=l.useState();return((0,tr.b)(()=>{o(new DocumentFragment)},[]),n.open)?(0,v.jsx)(nW,{...e,ref:t}):r?a.createPortal((0,v.jsx)(nD,{scope:e.__scopeSelect,children:(0,v.jsx)(nh.Slot,{scope:e.__scopeSelect,children:(0,v.jsx)("div",{children:e.children})})}),r):null});nj.displayName=nM;var[nD,nN]=ng(nM),nO=(0,tC.Z8)("SelectContent.RemoveScroll"),nW=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:a,side:u,sideOffset:s,align:f,alignOffset:p,arrowPadding:h,collisionBoundary:m,collisionPadding:g,sticky:y,hideWhenDetached:b,avoidCollisions:x,...C}=e,R=nx(nM,n),[T,A]=l.useState(null),[P,k]=l.useState(null),M=(0,d.e)(t,e=>A(e)),[j,D]=l.useState(null),[N,O]=l.useState(null),W=nm(n),[I,H]=l.useState(!1),F=l.useRef(!1);l.useEffect(()=>{if(T)return tN(T)},[T]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??S()),document.body.insertAdjacentElement("beforeend",e[1]??S()),E++,()=>{1===E&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),E--}},[]);let V=l.useCallback(e=>{let[t,...n]=W().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),n?.focus(),document.activeElement!==o))return},[W,P]),B=l.useCallback(()=>V([j,T]),[V,j,T]);l.useEffect(()=>{I&&B()},[I,B]);let{onOpenChange:_,triggerPointerDownPosRef:z}=R;l.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||_(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,_,z]),l.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[K,$]=rn(e=>{let t=W().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=rr(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Y=l.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==R.value&&R.value===t||r)&&(D(e),r&&(F.current=!0))},[R.value]),U=l.useCallback(()=>T?.focus(),[T]),Z=l.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==R.value&&R.value===t||r)&&O(e)},[R.value]),X="popper"===r?nH:nI,q=X===nH?{side:u,sideOffset:s,align:f,alignOffset:p,arrowPadding:h,collisionBoundary:m,collisionPadding:g,sticky:y,hideWhenDetached:b,avoidCollisions:x}:{};return(0,v.jsx)(nD,{scope:n,content:T,viewport:P,onViewportChange:k,itemRefCallback:Y,selectedItem:j,onItemLeave:U,itemTextRefCallback:Z,focusSelectedItem:B,selectedItemText:N,position:r,isPositioned:I,searchRef:K,children:(0,v.jsx)(ns,{as:nO,allowPinchZoom:!0,children:(0,v.jsx)(L,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,c.M)(o,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,v.jsx)(w,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,v.jsx)(X,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...q,onPlaced:()=>H(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,c.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=W().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>V(t)),e.preventDefault()}})})})})})})});nW.displayName="SelectContentImpl";var nI=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nx(nM,n),a=nN(nM,n),[c,s]=l.useState(null),[f,p]=l.useState(null),m=(0,d.e)(t,e=>p(e)),g=nm(n),y=l.useRef(!1),w=l.useRef(!0),{viewport:b,selectedItem:x,selectedItemText:E,focusSelectedItem:S}=a,C=l.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&f&&b&&x&&E){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,a=e.width+l,s=Math.max(a,t.width),d=u(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=a+"px",c.style.left=d+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,a=e.width+l,s=Math.max(a,t.width),d=u(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=a+"px",c.style.right=d+"px"}let l=g(),a=window.innerHeight-20,s=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),v=p+h+s+parseInt(d.paddingBottom,10)+m,w=Math.min(5*x.offsetHeight,v),S=window.getComputedStyle(b),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,L=x.offsetHeight/2,A=p+h+(x.offsetTop+L);if(A<=T){let e=l.length>0&&x===l[l.length-1].ref.current;c.style.bottom="0px";let t=f.clientHeight-b.offsetTop-b.offsetHeight;c.style.height=A+Math.max(a-T,L+(e?R:0)+t+m)+"px"}else{let e=l.length>0&&x===l[0].ref.current;c.style.top="0px";let t=Math.max(T,p+b.offsetTop+(e?C:0)+L);c.style.height=t+(v-A)+"px",b.scrollTop=A-T+b.offsetTop}c.style.margin="10px 0",c.style.minHeight=w+"px",c.style.maxHeight=a+"px",r?.(),requestAnimationFrame(()=>y.current=!0)}},[g,i.trigger,i.valueNode,c,f,b,x,E,i.dir,r]);(0,tr.b)(()=>C(),[C]);let[R,T]=l.useState();(0,tr.b)(()=>{f&&T(window.getComputedStyle(f).zIndex)},[f]);let L=l.useCallback(e=>{e&&!0===w.current&&(C(),S?.(),w.current=!1)},[C,S]);return(0,v.jsx)(nF,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:y,onScrollButtonChange:L,children:(0,v.jsx)("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,v.jsx)(h.WV.div,{...o,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nI.displayName="SelectItemAlignedPosition";var nH=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nw(n);return(0,v.jsx)(tv,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nH.displayName="SelectPopperPosition";var[nF,nV]=ng(nM,{}),nB="SelectViewport",n_=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nN(nB,n),a=nV(nB,n),u=(0,d.e)(t,i.onViewportChange),s=l.useRef(0);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,v.jsx)(nh.Slot,{scope:n,children:(0,v.jsx)(h.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,c.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if(r?.current&&n){let e=Math.abs(s.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}s.current=t.scrollTop})})})]})});n_.displayName=nB;var nz="SelectGroup",[nK,n$]=ng(nz),nY=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,D.M)();return(0,v.jsx)(nK,{scope:n,id:o,children:(0,v.jsx)(h.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nY.displayName=nz;var nU="SelectLabel",nZ=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=n$(nU,n);return(0,v.jsx)(h.WV.div,{id:o.id,...r,ref:t})});nZ.displayName=nU;var nX="SelectItem",[nq,nG]=ng(nX),nJ=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...a}=e,u=nx(nX,n),s=nN(nX,n),f=u.value===r,[p,m]=l.useState(i??""),[g,y]=l.useState(!1),w=(0,d.e)(t,e=>s.itemRefCallback?.(e,r,o)),b=(0,D.M)(),x=l.useRef("touch"),E=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,v.jsx)(nq,{scope:n,value:r,disabled:o,textId:b,isSelected:f,onItemTextChange:l.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,v.jsx)(nh.ItemSlot,{scope:n,value:r,disabled:o,textValue:p,children:(0,v.jsx)(h.WV.div,{role:"option","aria-labelledby":b,"data-highlighted":g?"":void 0,"aria-selected":f&&g,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:w,onFocus:(0,c.M)(a.onFocus,()=>y(!0)),onBlur:(0,c.M)(a.onBlur,()=>y(!1)),onClick:(0,c.M)(a.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:(0,c.M)(a.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:(0,c.M)(a.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,c.M)(a.onPointerMove,e=>{x.current=e.pointerType,o?s.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,c.M)(a.onPointerLeave,e=>{e.currentTarget===document.activeElement&&s.onItemLeave?.()}),onKeyDown:(0,c.M)(a.onKeyDown,e=>{s.searchRef?.current!==""&&" "===e.key||(nf.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});nJ.displayName=nX;var nQ="SelectItemText",n0=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,u=nx(nQ,n),c=nN(nQ,n),s=nG(nQ,n),f=nS(nQ,n),[p,m]=l.useState(null),g=(0,d.e)(t,e=>m(e),s.onItemTextChange,e=>c.itemTextRefCallback?.(e,s.value,s.disabled)),y=p?.textContent,w=l.useMemo(()=>(0,v.jsx)("option",{value:s.value,disabled:s.disabled,children:y},s.value),[s.disabled,s.value,y]),{onNativeOptionAdd:b,onNativeOptionRemove:x}=f;return(0,tr.b)(()=>(b(w),()=>x(w)),[b,x,w]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(h.WV.span,{id:s.textId,...i,ref:g}),s.isSelected&&u.valueNode&&!u.valueNodeHasChildren?a.createPortal(i.children,u.valueNode):null]})});n0.displayName=nQ;var n1="SelectItemIndicator",n2=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nG(n1,n).isSelected?(0,v.jsx)(h.WV.span,{"aria-hidden":!0,...r,ref:t}):null});n2.displayName=n1;var n5="SelectScrollUpButton",n6=l.forwardRef((e,t)=>{let n=nN(n5,e.__scopeSelect),r=nV(n5,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,d.e)(t,r.onScrollButtonChange);return(0,tr.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(n9,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});n6.displayName=n5;var n7="SelectScrollDownButton",n3=l.forwardRef((e,t)=>{let n=nN(n7,e.__scopeSelect),r=nV(n7,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,d.e)(t,r.onScrollButtonChange);return(0,tr.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(n9,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});n3.displayName=n7;var n9=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nN("SelectScrollButton",n),a=l.useRef(null),u=nm(n),s=l.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return l.useEffect(()=>()=>s(),[s]),(0,tr.b)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,v.jsx)(h.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,c.M)(o.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(r,50))}),onPointerMove:(0,c.M)(o.onPointerMove,()=>{i.onItemLeave?.(),null===a.current&&(a.current=window.setInterval(r,50))}),onPointerLeave:(0,c.M)(o.onPointerLeave,()=>{s()})})}),n4=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,v.jsx)(h.WV.div,{"aria-hidden":!0,...r,ref:t})});n4.displayName="SelectSeparator";var n8="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nw(n),i=nx(n8,n),l=nN(n8,n);return i.open&&"popper"===l.position?(0,v.jsx)(tw,{...o,...r,ref:t}):null}).displayName=n8;var re=l.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{let o=l.useRef(null),i=(0,d.e)(r,o),a=(0,tT.D)(t);return l.useEffect(()=>{let e=o.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,v.jsx)(h.WV.select,{...n,style:{...tL,...n.style},ref:i,defaultValue:t})});function rt(e){return""===e||void 0===e}function rn(e){let t=(0,m.W)(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function rr(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}re.displayName="SelectBubbleInput";var ro=nC,ri=nT,rl=nA,ra=nP,ru=nk,rc=nj,rs=n_,rd=nY,rf=nZ,rp=nJ,rh=n0,rm=n2,rv=n6,rg=n3,ry=n4},53405:(e,t,n)=>{n.d(t,{D:()=>o});var r=n(17577);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},2566:(e,t,n)=>{n.d(t,{t:()=>i});var r=n(17577),o=n(65819);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}};