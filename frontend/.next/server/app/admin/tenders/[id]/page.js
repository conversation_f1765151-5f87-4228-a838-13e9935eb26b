(()=>{var e={};e.id=5305,e.ids=[5305,3156],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},80290:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o}),t(66045),t(90596),t(21851),t(26083),t(19644),t(96560);var a=t(23191),r=t(88716),i=t(37922),n=t.n(i),l=t(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["admin",{children:["tenders",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,66045)),"/Users/<USER>/Desktop/brid1/frontend/app/admin/tenders/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,90596)),"/Users/<USER>/Desktop/brid1/frontend/app/admin/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21851)),"/Users/<USER>/Desktop/brid1/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,19644)),"/Users/<USER>/Desktop/brid1/frontend/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,96560)),"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx"]}],c=["/Users/<USER>/Desktop/brid1/frontend/app/admin/tenders/[id]/page.tsx"],m="/admin/tenders/[id]/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/tenders/[id]/page",pathname:"/admin/tenders/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},54139:(e,s,t)=>{Promise.resolve().then(t.bind(t,2802))},85658:(e,s,t)=>{Promise.resolve().then(t.bind(t,77651))},2802:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(10326),r=t(33136),i=t(8633);function n({children:e}){let{user:s}=(0,r.a)();return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[a.jsx("div",{className:"absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-blue-400 to-cyan-300 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob"}),a.jsx("div",{className:"absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-purple-400 to-pink-300 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob animation-delay-2000"}),a.jsx("div",{className:"absolute top-40 left-1/2 w-96 h-96 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob animation-delay-4000"})]}),(0,a.jsxs)("div",{className:"flex h-screen relative z-10",children:[a.jsx(i.Z,{userRole:s?.role}),a.jsx("div",{className:"flex-1 flex flex-col overflow-hidden",children:a.jsx("main",{className:"flex-1 overflow-y-auto p-6 relative",children:a.jsx("div",{className:"max-w-7xl mx-auto",children:e})})})]})]})}},77651:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(10326),r=t(17577),i=t(35047),n=t(33071),l=t(90772),d=t(567),o=t(51956),c=t(6824),m=t(86333),u=t(71821),p=t(37358),h=t(48998),x=t(79635),g=t(38787),y=t(36283),v=t(54659),b=t(91470);function f(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{toast:t}=(0,o.p)(),f=e.id,[j,N]=(0,r.useState)(null),[w,k]=(0,r.useState)(!0),[Z,$]=(0,r.useState)([]),[z,q]=(0,r.useState)(!1),M=async e=>{try{await c.ZP.put(`/admin/tenders/${f}/status`,{status:e}),N(s=>s?{...s,status:e}:null),t({title:"نجح",description:`تم تحديث حالة المناقصة إلى ${e}`})}catch(e){console.error("Error updating tender status:",e),t({title:"خطأ",description:"فشل في تحديث حالة المناقصة",variant:"destructive"})}},P=async(e,s)=>{try{await c.ZP.put(`/admin/tenders/${f}/submissions/${e}/status`,{status:s}),$(t=>t.map(t=>t.id===e?{...t,status:s}:t)),t({title:"نجح",description:`تم تحديث حالة التقديم إلى ${s}`})}catch(e){console.error("Error updating submission status:",e),t({title:"خطأ",description:"فشل في تحديث حالة التقديم",variant:"destructive"})}};return w?a.jsx("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-8 bg-gray-300 rounded w-1/3 mb-4"}),a.jsx("div",{className:"h-4 bg-gray-300 rounded w-1/2 mb-8"}),(0,a.jsxs)("div",{className:"grid gap-4",children:[a.jsx("div",{className:"h-32 bg-gray-300 rounded"}),a.jsx("div",{className:"h-32 bg-gray-300 rounded"})]})]})}):j?(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,a.jsxs)(l.z,{variant:"ghost",onClick:()=>s.back(),children:[a.jsx(m.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),a.jsx("h1",{className:"text-2xl font-bold",children:"Tender Details"})]}),(0,a.jsxs)("div",{className:"grid gap-6",children:[(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[a.jsx(n.ll,{className:"text-xl",children:j.title}),(0,a.jsxs)(n.SZ,{className:"mt-2",children:[j.organizer.profile.governmentEntity||j.organizer.profile.companyName||"منظم"," • ",j.category]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(d.C,{variant:"open"===j.status?"default":"closed"===j.status?"secondary":"awarded"===j.status?"destructive":"outline",children:j.status}),"open"===j.status&&(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>M("closed"),children:"Close Tender"}),a.jsx(l.z,{variant:"destructive",size:"sm",onClick:()=>M("awarded"),children:"Award"})]})]})]})}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold mb-2",children:"Description"}),a.jsx("p",{className:"text-sm text-gray-600 mb-4",children:j.description}),a.jsx("h3",{className:"font-semibold mb-2",children:"Requirements"}),a.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:j.requirements.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[a.jsx("span",{className:"text-blue-500",children:"•"}),e]},s))})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(u.Z,{className:"w-4 h-4 text-green-500"}),(0,a.jsxs)("span",{className:"font-semibold",children:["Budget: $",j.budget.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(p.Z,{className:"w-4 h-4 text-blue-500"}),(0,a.jsxs)("span",{children:["Start: ",new Date(j.startDate).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(h.Z,{className:"w-4 h-4 text-orange-500"}),(0,a.jsxs)("span",{children:["End: ",new Date(j.deadline).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(x.Z,{className:"w-4 h-4 text-purple-500"}),(0,a.jsxs)("span",{children:["Created by: ",j.organizer.profile.fullName||j.organizer.profile.companyName||j.organizer.email]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(g.Z,{className:"w-4 h-4 text-gray-500"}),(0,a.jsxs)("span",{children:["Department: ",j.organizer.profile.governmentEntity||"N/A"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(y.Z,{className:"w-4 h-4 text-blue-500"}),(0,a.jsxs)("span",{children:["Submissions: ",j.proposals.length]})]})]})]})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{children:["Submissions (",Z.length,")"]}),a.jsx(n.SZ,{children:"Companies that have submitted proposals for this tender"})]}),a.jsx(n.aY,{children:z?a.jsx("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"animate-pulse border rounded-lg p-4",children:[a.jsx("div",{className:"h-4 bg-gray-300 rounded w-1/3 mb-2"}),a.jsx("div",{className:"h-3 bg-gray-300 rounded w-1/2"})]},s))}):0===Z.length?a.jsx("p",{className:"text-gray-500 text-center py-8",children:"No submissions yet"}):a.jsx("div",{className:"space-y-4",children:Z.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold",children:e.companyName}),a.jsx("p",{className:"text-sm text-gray-600",children:e.contactEmail}),a.jsx("p",{className:"text-sm text-gray-600",children:e.contactPhone})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-semibold text-lg text-green-600",children:["$",e.proposedAmount.toLocaleString()]}),a.jsx(d.C,{variant:"approved"===e.status?"default":"rejected"===e.status?"destructive":"secondary",children:e.status})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[a.jsx(p.Z,{className:"w-4 h-4"}),"Submitted: ",new Date(e.submittedAt).toLocaleDateString()]}),"pending"===e.status&&(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>P(e.id,"approved"),children:[a.jsx(v.Z,{className:"w-4 h-4 mr-2"}),"Approve"]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>P(e.id,"rejected"),children:[a.jsx(b.Z,{className:"w-4 h-4 mr-2"}),"Reject"]})]})]})]},e.id))})})]})]})]}):(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("div",{className:"flex items-center gap-2 mb-4",children:(0,a.jsxs)(l.z,{variant:"ghost",onClick:()=>s.back(),children:[a.jsx(m.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-8 text-center",children:a.jsx("p",{className:"text-gray-500",children:"Tender not found"})})})]})}},8633:(e,s,t)=>{"use strict";t.d(s,{Z:()=>$});var a=t(10326),r=t(17577),i=t(90434),n=t(35047),l=t(90772),d=t(24319),o=t(24061),c=t(58038),m=t(40167),u=t(36283),p=t(51996),h=t(88378),x=t(17069),g=t(38787),y=t(58907),v=t(66697),b=t(6507),f=t(79635),j=t(26092),N=t(11890),w=t(39183),k=t(71810);let Z=[{href:"/admin/dashboard",label:"لوحة التحكم",icon:a.jsx(d.Z,{className:"h-5 w-5"}),roles:["admin","super_admin"]},{href:"/admin/pending-accounts",label:"الحسابات المعلقة",icon:a.jsx(o.Z,{className:"h-5 w-5"}),roles:["admin","super_admin"]},{href:"/admin/users",label:"إدارة المستخدمين",icon:a.jsx(c.Z,{className:"h-5 w-5"}),roles:["admin","super_admin"]},{href:"/admin/auctions",label:"إدارة المزادات",icon:a.jsx(m.Z,{className:"h-5 w-5"}),roles:["admin","super_admin"]},{href:"/admin/tenders",label:"إدارة المناقصات",icon:a.jsx(u.Z,{className:"h-5 w-5"}),roles:["admin","super_admin"]},{href:"/admin/create-tender",label:"إنشاء مناقصة",icon:a.jsx(p.Z,{className:"h-5 w-5"}),roles:["admin","super_admin"]},{href:"/admin/settings",label:"الإعدادات",icon:a.jsx(h.Z,{className:"h-5 w-5"}),roles:["super_admin"]},{href:"/company/dashboard",label:"لوحة التحكم",icon:a.jsx(d.Z,{className:"h-5 w-5"}),roles:["company"]},{href:"/company/auctions",label:"مزاداتي",icon:a.jsx(m.Z,{className:"h-5 w-5"}),roles:["company"]},{href:"/company/tenders",label:"مناقصاتي",icon:a.jsx(u.Z,{className:"h-5 w-5"}),roles:["company"]},{href:"/company/create-auction",label:"إنشاء مزاد",icon:a.jsx(p.Z,{className:"h-5 w-5"}),roles:["company"]},{href:"/company/bids",label:"عطاءاتي",icon:a.jsx(x.Z,{className:"h-5 w-5"}),roles:["company"]},{href:"/company/profile",label:"الملف الشخصي",icon:a.jsx(g.Z,{className:"h-5 w-5"}),roles:["company"]},{href:"/user/dashboard",label:"لوحة التحكم",icon:a.jsx(d.Z,{className:"h-5 w-5"}),roles:["individual"]},{href:"/user/leaderboard",label:"لوحة الصدارة",icon:a.jsx(y.Z,{className:"h-5 w-5"}),roles:["individual"]},{href:"/user/auctions",label:"المزادات المتاحة",icon:a.jsx(m.Z,{className:"h-5 w-5"}),roles:["individual"]},{href:"/user/my-bids",label:"مزايداتي",icon:a.jsx(v.Z,{className:"h-5 w-5"}),roles:["individual"]},{href:"/user/notifications",label:"الإشعارات",icon:a.jsx(b.Z,{className:"h-5 w-5"}),roles:["individual"]},{href:"/user/profile",label:"الملف الشخصي",icon:a.jsx(f.Z,{className:"h-5 w-5"}),roles:["individual"]},{href:"/government/dashboard",label:"لوحة التحكم",icon:a.jsx(d.Z,{className:"h-5 w-5"}),roles:["government"]},{href:"/government/tenders",label:"مناقصاتي",icon:a.jsx(u.Z,{className:"h-5 w-5"}),roles:["government"]},{href:"/government/create-tender",label:"إنشاء مناقصة",icon:a.jsx(p.Z,{className:"h-5 w-5"}),roles:["government"]},{href:"/government/applications",label:"طلبات المشاركة",icon:a.jsx(o.Z,{className:"h-5 w-5"}),roles:["government"]},{href:"/government/profile",label:"الملف الشخصي",icon:a.jsx(c.Z,{className:"h-5 w-5"}),roles:["government"]}];function $({userRole:e}){let[s,t]=(0,r.useState)(!1),[d,o]=(0,r.useState)(null),m=(0,n.useRouter)(),u=(0,n.usePathname)(),p=Z.filter(e=>e.roles.includes(d?.role||""));return(0,a.jsxs)("div",{className:`${s?"w-16":"w-56"} transition-all duration-300 backdrop-blur-xl bg-white/90 border-r border-white/20 shadow-xl flex flex-col h-screen relative z-10`,children:[a.jsx("div",{className:"p-4 border-b border-gray-200/50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[!s&&(0,a.jsxs)(i.default,{href:"/",className:"flex items-center gap-3 group",children:[a.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg",children:a.jsx(j.Z,{className:"w-4 h-4 text-white"})}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("h1",{className:"text-base font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"المنصة"}),a.jsx("p",{className:"text-xs text-gray-600 font-medium",children:"المزادات والمناقصات"})]})]}),s&&a.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg mx-auto",children:a.jsx(j.Z,{className:"w-4 h-4 text-white"})}),a.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>t(!s),className:"p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200",children:s?a.jsx(N.Z,{className:"h-3 w-3"}):a.jsx(w.Z,{className:"h-3 w-3"})})]})}),d&&a.jsx("div",{className:"px-4 py-4 border-b border-gray-200/50",children:(0,a.jsxs)("div",{className:`flex items-center ${s?"justify-center":"gap-3"}`,children:[a.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg",children:(()=>{switch(d?.role){case"super_admin":return a.jsx(j.Z,{className:"h-6 w-6 text-yellow-500"});case"admin":return a.jsx(c.Z,{className:"h-6 w-6 text-blue-500"});case"company":return a.jsx(g.Z,{className:"h-6 w-6 text-green-500"});case"government":return a.jsx(c.Z,{className:"h-6 w-6 text-red-500"});case"individual":return a.jsx(f.Z,{className:"h-6 w-6 text-purple-500"});default:return a.jsx(f.Z,{className:"h-6 w-6"})}})()}),!s&&(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-bold text-gray-900 truncate",children:d.profile?.fullName||d.profile?.companyName||d.profile?.governmentEntity||"المدير"}),a.jsx("p",{className:"text-xs text-gray-600 font-medium truncate",children:(e=>{switch(e){case"super_admin":return"مدير عام";case"admin":return"مدير";case"company":return"شركة";case"government":return"جهة حكومية";case"individual":return"فرد";default:return e}})(d.role)}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 mt-1",children:[a.jsx("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"}),a.jsx("span",{className:"text-xs text-gray-500",children:"متصل"})]})]})]})}),a.jsx("nav",{className:"flex-1 px-4 py-4",children:a.jsx("div",{className:"space-y-2",children:p.map(e=>{let t=u===e.href;return(0,a.jsxs)(i.default,{href:e.href,className:`group flex items-center ${s?"justify-center px-2 py-3":"gap-3 px-4 py-3"} rounded-xl transition-all duration-300 relative overflow-hidden ${t?"bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-indigo-500/10 border border-blue-300/30 shadow-md backdrop-blur-sm":"hover:bg-white/60 hover:shadow-md hover:scale-[1.02] border border-transparent"}`,children:[t&&a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"}),a.jsx("div",{className:`${s?"w-6 h-6":"w-8 h-8"} rounded-lg flex items-center justify-center transition-all duration-300 relative z-10 ${t?"bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg scale-105":"bg-gray-100 group-hover:bg-gradient-to-br group-hover:from-gray-200 group-hover:to-gray-300"}`,children:a.jsx("div",{className:`transition-all duration-300 ${t?"text-white scale-105":"text-gray-600 group-hover:text-gray-700"}`,children:e.icon})}),!s&&(0,a.jsxs)("div",{className:"relative z-10",children:[a.jsx("span",{className:`text-sm font-semibold transition-all duration-300 ${t?"text-gray-900":"text-gray-700 group-hover:text-gray-900"}`,children:e.label}),t&&a.jsx("div",{className:"w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-0.5"})]}),t&&!s&&a.jsx(w.Z,{className:"w-4 h-4 text-blue-600 mr-auto relative z-10"})]},e.href)})})}),a.jsx("div",{className:"px-4 pb-4",children:a.jsx("div",{className:"border-t border-gray-200/50 pt-4",children:(0,a.jsxs)(l.z,{onClick:()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),m.push("/auth/login")},variant:"ghost",className:`w-full group flex items-center ${s?"justify-center px-2 py-3":"gap-3 px-4 py-3"} rounded-xl hover:bg-red-50/80 hover:shadow-md transition-all duration-300 border border-transparent hover:border-red-200/50`,children:[a.jsx("div",{className:`${s?"w-6 h-6":"w-8 h-8"} rounded-lg bg-red-100 group-hover:bg-gradient-to-br group-hover:from-red-500 group-hover:to-pink-500 flex items-center justify-center transition-all duration-300`,children:a.jsx(k.Z,{className:`${s?"w-3 h-3":"w-4 h-4"} text-red-600 group-hover:text-white transition-all duration-300`})}),!s&&a.jsx("span",{className:"text-sm font-semibold text-red-700 group-hover:text-red-800 transition-all duration-300",children:"تسجيل الخروج"})]})})})]})}},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>l});var a=t(10326);t(17577);var r=t(79360),i=t(77863);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...t}){return a.jsx("div",{className:(0,i.cn)(n({variant:s}),e),...t})}},6824:(e,s,t)=>{"use strict";t.d(s,{Sb:()=>l,Xy:()=>n,ZP:()=>d,kv:()=>r,zg:()=>i});let a=t(44099).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});a.interceptors.request.use(e=>{let s=localStorage.getItem("token");return s&&(e.headers.Authorization=`Bearer ${s}`),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)));let r={register:e=>a.post("/auth/register",e),login:e=>a.post("/auth/login",e),logout:()=>a.post("/auth/logout"),verifyEmail:e=>a.post("/auth/verify-email",{token:e}),resendVerification:e=>a.post("/auth/resend-verification",{email:e}),forgotPassword:e=>a.post("/auth/forgot-password",{email:e}),resetPassword:e=>a.post("/auth/reset-password",e)},i={getAll:()=>a.get("/auctions"),getById:e=>a.get(`/auctions/${e}`),create:e=>a.post("/auctions",e),update:(e,s)=>a.put(`/auctions/${e}`,s),delete:e=>a.delete(`/auctions/${e}`),placeBid:(e,s)=>a.post(`/auctions/${e}/bid`,{amount:s})},n={getFavorites:e=>a.get("/favorites",{params:e}),addFavorite:e=>a.post("/favorites",e),removeFavorite:(e,s)=>a.delete(`/favorites/${e}/${s}`),updateFavorite:(e,s,t)=>a.put(`/favorites/${e}/${s}`,t),checkFavorite:(e,s)=>a.get(`/favorites/check/${e}/${s}`)},l={getDashboardStats:()=>a.get("/admin/dashboard"),getPendingAccounts:()=>a.get("/admin/pending-accounts"),approvePendingAccount:e=>a.post(`/admin/pending-accounts/${e}/approve`),rejectPendingAccount:(e,s)=>a.post(`/admin/pending-accounts/${e}/reject`,{reason:s}),users:{getAll:e=>a.get("/admin/users",{params:e}),getById:e=>a.get(`/admin/users/${e}`),update:(e,s)=>a.put(`/admin/users/${e}`,s),delete:e=>a.delete(`/admin/users/${e}`),activate:e=>a.post(`/admin/users/${e}/activate`),deactivate:e=>a.post(`/admin/users/${e}/deactivate`)},auctions:{getAll:e=>a.get("/admin/auctions",{params:e}),getById:e=>a.get(`/admin/auctions/${e}`),approve:e=>a.post(`/admin/auctions/${e}/approve`),reject:(e,s)=>a.post(`/admin/auctions/${e}/reject`,{reason:s}),suspend:(e,s)=>a.post(`/admin/auctions/${e}/suspend`,{reason:s}),delete:e=>a.delete(`/admin/auctions/${e}`)},tenders:{getAll:e=>a.get("/admin/tenders",{params:e}),getById:e=>a.get(`/admin/tenders/${e}`),approve:e=>a.post(`/admin/tenders/${e}/approve`),reject:(e,s)=>a.post(`/admin/tenders/${e}/reject`,{reason:s}),suspend:(e,s)=>a.post(`/admin/tenders/${e}/suspend`,{reason:s}),delete:e=>a.delete(`/admin/tenders/${e}`)},getTender:e=>a.get(`/admin/tenders/${e}`),getTenderSubmissions:(e,s)=>a.get(`/admin/tenders/${e}/submissions`,{params:s}),updateTenderStatus:(e,s)=>a.put(`/admin/tenders/${e}/status`,s),updateTenderSubmissionStatus:(e,s,t)=>a.put(`/admin/tenders/${e}/submissions/${s}/status`,t),reports:{getFinancialReport:e=>a.get("/admin/reports/financial",{params:e}),getUserReport:e=>a.get("/admin/reports/users",{params:e}),getActivityReport:e=>a.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>a.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>a.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>a.get("/admin/settings"),update:e=>a.put("/admin/settings",e),backup:()=>a.post("/admin/settings/backup"),restore:e=>a.post(`/admin/settings/restore/${e}`)}},d=a},66697:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},6507:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},38787:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},37358:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},54659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},11890:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},39183:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},26092:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},71821:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},36283:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},40167:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Gavel",[["path",{d:"m14 13-7.5 7.5c-.83.83-2.17.83-3 0 0 0 0 0 0 0a2.12 2.12 0 0 1 0-3L11 10",key:"c9cbz0"}],["path",{d:"m16 16 6-6",key:"vzrcl6"}],["path",{d:"m8 8 6-6",key:"18bi4p"}],["path",{d:"m9 7 8 8",key:"5jnvq1"}],["path",{d:"m21 11-8-8",key:"z4y7zo"}]])},24319:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},71810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},51996:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("PlusCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},88378:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},17069:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},58907:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},79635:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},24061:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},91470:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},90596:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/app/admin/layout.tsx#default`)},66045:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/app/admin/tenders/[id]/page.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[2116,4099,5540],()=>t(80290));module.exports=a})();