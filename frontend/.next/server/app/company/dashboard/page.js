(()=>{var e={};e.id=1482,e.ids=[1482],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},87377:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>l,originalPathname:()=>c,pages:()=>u,routeModule:()=>h,tree:()=>p}),r(92823),r(21851),r(26083),r(19644),r(96560);var s=r(23191),a=r(88716),i=r(37922),n=r.n(i),d=r(95231),o={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);r.d(t,o);let p=["",{children:["company",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,92823)),"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,21851)),"/Users/<USER>/Desktop/brid1/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,19644)),"/Users/<USER>/Desktop/brid1/frontend/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,96560)),"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx"]}],u=["/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx"],c="/company/dashboard/page",l={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/company/dashboard/page",pathname:"/company/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},90678:(e,t,r)=>{Promise.resolve().then(r.bind(r,97663))},97663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(10326),a=r(17577);r(567);var i=r(51956),n=r(27817),d=r(35047);function o(){let[e,t]=(0,a.useState)(null),[r,o]=(0,a.useState)([]),[p,u]=(0,a.useState)([]),[c,l]=(0,a.useState)([]),[h,y]=(0,a.useState)(!0),[g,m]=(0,a.useState)(null),[v,x]=(0,a.useState)({revenueOverTime:[],auctionPerformance:[],categoryDistribution:[],bidderActivity:[]}),{toast:k}=(0,i.p)();return((0,d.useRouter)(),h)?s.jsx("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-600",children:"جاري تحميل بيانات الشركة..."})]})}):s.jsx(n.default,{allowedRoles:["company"],children:s.jsx("div",{className:"space-y-6",children:(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"لوحة تحكم الشركة"}),s.jsx("p",{className:"text-gray-600",children:"مرحباً بك في لوحة تحكم شركتك"})]})})})}r(6824)},567:(e,t,r)=>{"use strict";r.d(t,{C:()=>d});var s=r(10326);r(17577);var a=r(79360),i=r(77863);let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...r}){return s.jsx("div",{className:(0,i.cn)(n({variant:t}),e),...r})}},6824:(e,t,r)=>{"use strict";r.d(t,{Sb:()=>d,Xy:()=>n,ZP:()=>o,kv:()=>a,zg:()=>i});let s=r(44099).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)));let a={register:e=>s.post("/auth/register",e),login:e=>s.post("/auth/login",e),logout:()=>s.post("/auth/logout"),verifyEmail:e=>s.post("/auth/verify-email",{token:e}),resendVerification:e=>s.post("/auth/resend-verification",{email:e}),forgotPassword:e=>s.post("/auth/forgot-password",{email:e}),resetPassword:e=>s.post("/auth/reset-password",e)},i={getAll:()=>s.get("/auctions"),getById:e=>s.get(`/auctions/${e}`),create:e=>s.post("/auctions",e),update:(e,t)=>s.put(`/auctions/${e}`,t),delete:e=>s.delete(`/auctions/${e}`),placeBid:(e,t)=>s.post(`/auctions/${e}/bid`,{amount:t})},n={getFavorites:e=>s.get("/favorites",{params:e}),addFavorite:e=>s.post("/favorites",e),removeFavorite:(e,t)=>s.delete(`/favorites/${e}/${t}`),updateFavorite:(e,t,r)=>s.put(`/favorites/${e}/${t}`,r),checkFavorite:(e,t)=>s.get(`/favorites/check/${e}/${t}`)},d={getDashboardStats:()=>s.get("/admin/dashboard"),getPendingAccounts:()=>s.get("/admin/pending-accounts"),approvePendingAccount:e=>s.post(`/admin/pending-accounts/${e}/approve`),rejectPendingAccount:(e,t)=>s.post(`/admin/pending-accounts/${e}/reject`,{reason:t}),users:{getAll:e=>s.get("/admin/users",{params:e}),getById:e=>s.get(`/admin/users/${e}`),update:(e,t)=>s.put(`/admin/users/${e}`,t),delete:e=>s.delete(`/admin/users/${e}`),activate:e=>s.post(`/admin/users/${e}/activate`),deactivate:e=>s.post(`/admin/users/${e}/deactivate`)},auctions:{getAll:e=>s.get("/admin/auctions",{params:e}),getById:e=>s.get(`/admin/auctions/${e}`),approve:e=>s.post(`/admin/auctions/${e}/approve`),reject:(e,t)=>s.post(`/admin/auctions/${e}/reject`,{reason:t}),suspend:(e,t)=>s.post(`/admin/auctions/${e}/suspend`,{reason:t}),delete:e=>s.delete(`/admin/auctions/${e}`)},tenders:{getAll:e=>s.get("/admin/tenders",{params:e}),getById:e=>s.get(`/admin/tenders/${e}`),approve:e=>s.post(`/admin/tenders/${e}/approve`),reject:(e,t)=>s.post(`/admin/tenders/${e}/reject`,{reason:t}),suspend:(e,t)=>s.post(`/admin/tenders/${e}/suspend`,{reason:t}),delete:e=>s.delete(`/admin/tenders/${e}`)},getTender:e=>s.get(`/admin/tenders/${e}`),getTenderSubmissions:(e,t)=>s.get(`/admin/tenders/${e}/submissions`,{params:t}),updateTenderStatus:(e,t)=>s.put(`/admin/tenders/${e}/status`,t),updateTenderSubmissionStatus:(e,t,r)=>s.put(`/admin/tenders/${e}/submissions/${t}/status`,r),reports:{getFinancialReport:e=>s.get("/admin/reports/financial",{params:e}),getUserReport:e=>s.get("/admin/reports/users",{params:e}),getActivityReport:e=>s.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>s.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>s.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>s.get("/admin/settings"),update:e=>s.put("/admin/settings",e),backup:()=>s.post("/admin/settings/backup"),restore:e=>s.post(`/admin/settings/restore/${e}`)}},o=s},66697:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},6507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},38787:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},11890:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},39183:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},26092:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},36283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},40167:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Gavel",[["path",{d:"m14 13-7.5 7.5c-.83.83-2.17.83-3 0 0 0 0 0 0 0a2.12 2.12 0 0 1 0-3L11 10",key:"c9cbz0"}],["path",{d:"m16 16 6-6",key:"vzrcl6"}],["path",{d:"m8 8 6-6",key:"18bi4p"}],["path",{d:"m9 7 8 8",key:"5jnvq1"}],["path",{d:"m21 11-8-8",key:"z4y7zo"}]])},24319:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},71810:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},51996:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("PlusCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},88378:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},17069:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},58907:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},79635:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},24061:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},92823:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2116,4099,5540,7817],()=>r(87377));module.exports=s})();