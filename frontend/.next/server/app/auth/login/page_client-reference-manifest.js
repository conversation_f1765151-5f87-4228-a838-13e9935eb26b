globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth/login/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/Footer.tsx":{"*":{"id":"(ssr)/./components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/Navbar.tsx":{"*":{"id":"(ssr)/./components/layout/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(ssr)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.tsx":{"*":{"id":"(ssr)/./app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/login/page.tsx":{"*":{"id":"(ssr)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/layout/Footer.tsx":{"id":"(app-pages-browser)/./components/layout/Footer.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/layout/Navbar.tsx":{"id":"(app-pages-browser)/./components/layout/Navbar.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/contexts/AuthContext.tsx":{"id":"(app-pages-browser)/./contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx":{"id":"(app-pages-browser)/./app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx":{"id":"(app-pages-browser)/./app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/auth/login/page.tsx":{"id":"(app-pages-browser)/./app/auth/login/page.tsx","name":"*","chunks":["app/auth/login/page","static/chunks/app/auth/login/page.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Desktop/brid1/frontend/":[],"/Users/<USER>/Desktop/brid1/frontend/app/page":[],"/Users/<USER>/Desktop/brid1/frontend/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Desktop/brid1/frontend/app/error":[],"/Users/<USER>/Desktop/brid1/frontend/app/loading":[],"/Users/<USER>/Desktop/brid1/frontend/app/not-found":[],"/Users/<USER>/Desktop/brid1/frontend/app/auth/login/page":[]}}