(()=>{var e={};e.id=3225,e.ids=[3225],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},1464:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>c}),t(79385),t(21851),t(26083),t(19644),t(96560);var r=t(23191),a=t(88716),i=t(37922),l=t.n(i),n=t(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["user",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79385)),"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,21851)),"/Users/<USER>/Desktop/brid1/frontend/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,19644)),"/Users/<USER>/Desktop/brid1/frontend/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,96560)),"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx"]}],o=["/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx"],x="/user/dashboard/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/user/dashboard/page",pathname:"/user/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89140:(e,s,t)=>{Promise.resolve().then(t.bind(t,58940))},58940:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>K});var r=t(10326),a=t(17577),i=t(33071),l=t(90772),n=t(567),d=t(51956),c=t(27817),o=t(35047);t(6824);var x=t(88307),m=t(66697),u=t(6507),h=t(24061),p=t(3634),g=t(58907),j=t(17069),f=t(71821),b=t(67427),v=t(43727),N=t(15096),y=t(76464),w=t(24230),Z=t(48998),$=t(12714),k=t(53468),S=t(40167),P=t(30660),A=t(10669),q=t(18423),_=t(62637),z=t(12062),C=t(89495),R=t(41449),T=t(80679),D=t(77007),B=t(58620),M=t(99630),F=t(49844),Y=t(76385);function K(){let[e,s]=(0,a.useState)(null),[t,K]=(0,a.useState)([]),[O,E]=(0,a.useState)([]),[I,L]=(0,a.useState)([]),[U,G]=(0,a.useState)(!0),[Q,V]=(0,a.useState)(null),[X,H]=(0,a.useState)({biddingActivity:[],categorySpending:[],monthlyPerformance:[],winLossRatio:[]}),{toast:W}=(0,d.p)(),J=(0,o.useRouter)(),ee="#3B82F6",es="#10B981",et=e=>new Intl.NumberFormat("ar-SA",{style:"currency",currency:"SAR",minimumFractionDigits:0}).format(e),er=e=>{let s=new Date,t=new Date(e).getTime()-s.getTime();if(t<=0)return"انتهى";let r=Math.floor(t/864e5),a=Math.floor(t%864e5/36e5);return r>0?`${r} يوم`:a>0?`${a} ساعة`:`${Math.floor(t%36e5/6e4)} دقيقة`},ea=e=>{let s=new Date,t=new Date(e),r=s.getTime()-t.getTime(),a=Math.floor(r/6e4),i=Math.floor(r/36e5),l=Math.floor(r/864e5);return l>0?`منذ ${l} يوم`:i>0?`منذ ${i} ساعة`:a>0?`منذ ${a} دقيقة`:"الآن"},ei=e=>e.isWinning?{label:"أعلى مزايدة",variant:"default",color:"text-green-600"}:"active"===e.auction.status?{label:"متنافس",variant:"secondary",color:"text-blue-600"}:{label:"تم تجاوزها",variant:"outline",color:"text-gray-600"};return U?r.jsx(c.default,{allowedRoles:["individual"],children:r.jsx("div",{className:"text-center",children:r.jsx("p",{children:"جاري تحميل الإحصائيات..."})})}):r.jsx(c.default,{allowedRoles:["individual"],children:(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("header",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:["مرحباً، ",Q?.profile?.fullName||"المستخدم"]}),r.jsx("p",{className:"text-blue-100 mt-2",children:"استكشف المزادات المتاحة وتابع مزايداتك"})]}),(0,r.jsxs)("div",{className:"hidden md:flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[e?.successRate||0,"%"]}),r.jsx("div",{className:"text-sm text-blue-100",children:"معدل النجاح"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold",children:e?.totalBids||0}),r.jsx("div",{className:"text-sm text-blue-100",children:"إجمالي المزايدات"})]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)(l.z,{className:"h-16 text-lg bg-blue-600 hover:bg-blue-700",size:"lg",onClick:()=>J.push("/user/auctions"),children:[r.jsx(x.Z,{className:"h-6 w-6 ml-2"}),"استكشاف المزادات"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"h-16 text-lg border-green-200 text-green-700 hover:bg-green-50",size:"lg",onClick:()=>J.push("/user/my-bids"),children:[r.jsx(m.Z,{className:"h-6 w-6 ml-2"}),"مزايداتي"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50",size:"lg",onClick:()=>J.push("/user/notifications"),children:[r.jsx(u.Z,{className:"h-6 w-6 ml-2"}),"التنبيهات"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50",size:"lg",onClick:()=>J.push("/user/profile"),children:[r.jsx(h.Z,{className:"h-6 w-6 ml-2"}),"الملف الشخصي"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[r.jsx(i.Zb,{className:"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200",children:r.jsx(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-blue-600",children:"مزايداتي النشطة"}),r.jsx("p",{className:"text-3xl font-bold text-blue-900",children:e?.activeBids||0}),(0,r.jsxs)("p",{className:"text-xs text-blue-600 mt-1",children:[r.jsx(p.Z,{className:"h-3 w-3 inline ml-1"}),"نشط الآن"]})]}),r.jsx("div",{className:"h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center",children:r.jsx(m.Z,{className:"h-6 w-6 text-blue-600"})})]})})}),r.jsx(i.Zb,{className:"bg-gradient-to-br from-green-50 to-green-100 border-green-200",children:r.jsx(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-green-600",children:"المزادات المكسوبة"}),r.jsx("p",{className:"text-3xl font-bold text-green-900",children:e?.wonAuctions||0}),(0,r.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:[r.jsx(g.Z,{className:"h-3 w-3 inline ml-1"}),"إنجاز رائع"]})]}),r.jsx("div",{className:"h-12 w-12 bg-green-200 rounded-full flex items-center justify-center",children:r.jsx(g.Z,{className:"h-6 w-6 text-green-600"})})]})})}),r.jsx(i.Zb,{className:"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200",children:r.jsx(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-purple-600",children:"إجمالي المبلغ المنفق"}),r.jsx("p",{className:"text-3xl font-bold text-purple-900",children:et(e?.totalSpent||0)}),(0,r.jsxs)("p",{className:"text-xs text-purple-600 mt-1",children:[r.jsx(j.Z,{className:"h-3 w-3 inline ml-1"}),"استثمار ذكي"]})]}),r.jsx("div",{className:"h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center",children:r.jsx(f.Z,{className:"h-6 w-6 text-purple-600"})})]})})}),r.jsx(i.Zb,{className:"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200",children:r.jsx(i.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-orange-600",children:"المزادات المحفوظة"}),r.jsx("p",{className:"text-3xl font-bold text-orange-900",children:e?.savedAuctions||0}),(0,r.jsxs)("p",{className:"text-xs text-orange-600 mt-1",children:[r.jsx(b.Z,{className:"h-3 w-3 inline ml-1"}),"مفضلة"]})]}),r.jsx("div",{className:"h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center",children:r.jsx(b.Z,{className:"h-6 w-6 text-orange-600"})})]})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center gap-2",children:[r.jsx(v.Z,{className:"h-5 w-5 text-blue-600"}),"نشاط المزايدات الشهري"]}),r.jsx(i.SZ,{children:"عدد المزايدات والانتصارات خلال الأشهر الماضية"})]}),r.jsx(i.aY,{children:r.jsx(P.h,{width:"100%",height:300,children:(0,r.jsxs)(A.T,{data:X.biddingActivity,children:[r.jsx(q.q,{strokeDasharray:"3 3"}),r.jsx(_.K,{dataKey:"month"}),r.jsx(z.Q,{}),r.jsx(C.u,{}),r.jsx(R.D,{}),r.jsx(T.uN,{type:"monotone",dataKey:"bids",stackId:"1",stroke:ee,fill:ee,fillOpacity:.6,name:"المزايدات"}),r.jsx(T.uN,{type:"monotone",dataKey:"wins",stackId:"2",stroke:es,fill:es,fillOpacity:.8,name:"الانتصارات"})]})})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center gap-2",children:[r.jsx(N.Z,{className:"h-5 w-5 text-purple-600"}),"الإنفاق حسب الفئة"]}),r.jsx(i.SZ,{children:"توزيع إنفاقك على الفئات المختلفة"})]}),r.jsx(i.aY,{children:r.jsx(P.h,{width:"100%",height:300,children:(0,r.jsxs)(D.u,{children:[r.jsx(B.b,{data:X.categorySpending,cx:"50%",cy:"50%",labelLine:!1,label:({name:e,percent:s})=>`${e} ${(100*s).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:X.categorySpending.map((e,s)=>r.jsx(M.b,{fill:e.color},`cell-${s}`))}),r.jsx(C.u,{formatter:e=>et(e)})]})})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center gap-2",children:[r.jsx(y.Z,{className:"h-5 w-5 text-green-600"}),"الأداء الشهري"]}),r.jsx(i.SZ,{children:"المبلغ المنفق والمدخر شهرياً"})]}),r.jsx(i.aY,{children:r.jsx(P.h,{width:"100%",height:300,children:(0,r.jsxs)(F.v,{data:X.monthlyPerformance,children:[r.jsx(q.q,{strokeDasharray:"3 3"}),r.jsx(_.K,{dataKey:"month"}),r.jsx(z.Q,{}),r.jsx(C.u,{formatter:e=>et(e)}),r.jsx(R.D,{}),r.jsx(Y.$,{dataKey:"spent",fill:"#EF4444",name:"المنفق"}),r.jsx(Y.$,{dataKey:"saved",fill:es,name:"المدخر"})]})})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center gap-2",children:[r.jsx(g.Z,{className:"h-5 w-5 text-yellow-600"}),"نسبة النجاح"]}),r.jsx(i.SZ,{children:"نسبة الفوز إلى الخسارة في المزايدات"})]}),(0,r.jsxs)(i.aY,{children:[r.jsx(P.h,{width:"100%",height:300,children:(0,r.jsxs)(D.u,{children:[r.jsx(B.b,{data:X.winLossRatio,cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:"value",children:X.winLossRatio.map((e,s)=>r.jsx(M.b,{fill:e.color},`cell-${s}`))}),r.jsx(C.u,{formatter:e=>`${e}%`}),r.jsx(R.D,{})]})}),(0,r.jsxs)("div",{className:"mt-4 flex justify-center gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[X.winLossRatio[0]?.value||0,"%"]}),r.jsx("div",{className:"text-sm text-gray-600",children:"معدل النجاح"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[X.winLossRatio[1]?.value||0,"%"]}),r.jsx("div",{className:"text-sm text-gray-600",children:"معدل الخسارة"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(m.Z,{className:"h-5 w-5 text-blue-600"}),"مزايداتي الحديثة"]}),(0,r.jsxs)(l.z,{variant:"ghost",size:"sm",onClick:()=>J.push("/user/my-bids"),children:["عرض الكل",r.jsx(w.Z,{className:"h-4 w-4 mr-1"})]})]}),r.jsx(i.SZ,{children:"آخر المزايدات التي شاركت فيها"})]}),r.jsx(i.aY,{children:t.length>0?r.jsx("div",{className:"space-y-3",children:t.slice(0,3).map(e=>{let s=ei(e);return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h4",{className:"font-medium text-gray-900",children:e.auction.title}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mt-1",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[r.jsx(Z.Z,{className:"h-4 w-4 inline ml-1"}),er(e.auction.endTime)]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["مزايدتي: ",r.jsx("span",{className:"font-medium",children:et(e.amount)})]})]}),r.jsx("p",{className:"text-xs text-gray-500 mt-1",children:ea(e.placedAt)})]}),(0,r.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[r.jsx(n.C,{variant:s.variant,className:s.color,children:s.label}),(0,r.jsxs)(l.z,{size:"sm",variant:"outline",onClick:()=>J.push(`/auctions/${e.auction._id}`),children:[r.jsx($.Z,{className:"h-4 w-4 ml-1"}),"عرض"]})]})]},e._id)})}):(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(m.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد مزايدات حديثة"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"ابدأ بالمشاركة في المزادات المتاحة"}),r.jsx(l.z,{onClick:()=>J.push("/user/auctions"),children:"استكشاف المزادات"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(u.Z,{className:"h-5 w-5 text-orange-600"}),"التنبيهات",I.filter(e=>!e.read).length>0&&r.jsx(n.C,{variant:"destructive",className:"text-xs",children:I.filter(e=>!e.read).length})]}),(0,r.jsxs)(l.z,{variant:"ghost",size:"sm",onClick:()=>J.push("/user/notifications"),children:["عرض الكل",r.jsx(w.Z,{className:"h-4 w-4 mr-1"})]})]}),r.jsx(i.SZ,{children:"آخر التحديثات والإشعارات"})]}),r.jsx(i.aY,{children:I.length>0?r.jsx("div",{className:"space-y-3",children:I.slice(0,3).map(e=>r.jsx("div",{className:`p-3 border rounded-lg ${(e=>{switch(e){case"auction_won":return"bg-green-50 border-green-200 text-green-800";case"auction_ending":return"bg-yellow-50 border-yellow-200 text-yellow-800";case"new_auction":return"bg-blue-50 border-blue-200 text-blue-800";case"bid_outbid":return"bg-red-50 border-red-200 text-red-800";default:return"bg-gray-50 border-gray-200 text-gray-800"}})(e.type)} ${e.read?"":"ring-2 ring-blue-200"}`,children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm font-medium",children:e.title}),r.jsx("p",{className:"text-xs mt-1",children:e.message}),r.jsx("p",{className:"text-xs opacity-75 mt-2",children:ea(e.createdAt)})]}),!e.read&&r.jsx("div",{className:"w-2 h-2 bg-blue-600 rounded-full mt-1"})]})},e._id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(u.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد تنبيهات"}),r.jsx("p",{className:"text-gray-600",children:"ستظهر هنا التحديثات المهمة"})]})})]})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(k.Z,{className:"h-5 w-5 text-purple-600"}),"مزادات مقترحة لك"]}),(0,r.jsxs)(l.z,{variant:"ghost",size:"sm",onClick:()=>J.push("/user/auctions"),children:["عرض المزيد",r.jsx(w.Z,{className:"h-4 w-4 mr-1"})]})]}),r.jsx(i.SZ,{children:"مزادات قد تهمك بناءً على اهتماماتك السابقة"})]}),r.jsx(i.aY,{children:O.length>0?r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:O.slice(0,6).map(e=>(0,r.jsxs)("article",{className:"border rounded-lg p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-300 cursor-pointer",onClick:()=>J.push(`/auctions/${e._id}`),children:[r.jsx("div",{className:"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded mb-3 flex items-center justify-center",children:r.jsx(S.Z,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h4",{className:"font-medium text-gray-900 line-clamp-2",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["السعر الحالي: ",r.jsx("span",{className:"font-medium text-green-600",children:et(e.currentBid)})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["يبدأ من: ",et(e.startingBid)]})]}),r.jsx(n.C,{variant:"outline",className:"text-xs",children:e.category})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[r.jsx(Z.Z,{className:"h-3 w-3"}),er(e.endTime)]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[r.jsx(h.Z,{className:"h-3 w-3"}),e.bidsCount," مزايدة"]})]}),(0,r.jsxs)(l.z,{size:"sm",className:"w-full mt-3",onClick:s=>{s.stopPropagation(),J.push(`/auctions/${e._id}`)},children:[r.jsx($.Z,{className:"h-4 w-4 ml-1"}),"شاهد التفاصيل"]})]})]},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(k.Z,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"لا توجد مزادات متاحة حالياً"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"تحقق مرة أخرى لاحقاً أو استكشف الفئات المختلفة"}),r.jsx(l.z,{onClick:()=>J.push("/user/auctions"),children:"استكشاف المزادات"})]})})]})]})})}},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>n});var r=t(10326);t(17577);var a=t(79360),i=t(77863);let l=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,...t}){return r.jsx("div",{className:(0,i.cn)(l({variant:s}),e),...t})}},6824:(e,s,t)=>{"use strict";t.d(s,{Sb:()=>n,Xy:()=>l,ZP:()=>d,kv:()=>a,zg:()=>i});let r=t(44099).Z.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>{let s=localStorage.getItem("token");return s&&(e.headers.Authorization=`Bearer ${s}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("token"),window.location.href="/auth/login"),Promise.reject(e)));let a={register:e=>r.post("/auth/register",e),login:e=>r.post("/auth/login",e),logout:()=>r.post("/auth/logout"),verifyEmail:e=>r.post("/auth/verify-email",{token:e}),resendVerification:e=>r.post("/auth/resend-verification",{email:e}),forgotPassword:e=>r.post("/auth/forgot-password",{email:e}),resetPassword:e=>r.post("/auth/reset-password",e)},i={getAll:()=>r.get("/auctions"),getById:e=>r.get(`/auctions/${e}`),create:e=>r.post("/auctions",e),update:(e,s)=>r.put(`/auctions/${e}`,s),delete:e=>r.delete(`/auctions/${e}`),placeBid:(e,s)=>r.post(`/auctions/${e}/bid`,{amount:s})},l={getFavorites:e=>r.get("/favorites",{params:e}),addFavorite:e=>r.post("/favorites",e),removeFavorite:(e,s)=>r.delete(`/favorites/${e}/${s}`),updateFavorite:(e,s,t)=>r.put(`/favorites/${e}/${s}`,t),checkFavorite:(e,s)=>r.get(`/favorites/check/${e}/${s}`)},n={getDashboardStats:()=>r.get("/admin/dashboard"),getPendingAccounts:()=>r.get("/admin/pending-accounts"),approvePendingAccount:e=>r.post(`/admin/pending-accounts/${e}/approve`),rejectPendingAccount:(e,s)=>r.post(`/admin/pending-accounts/${e}/reject`,{reason:s}),users:{getAll:e=>r.get("/admin/users",{params:e}),getById:e=>r.get(`/admin/users/${e}`),update:(e,s)=>r.put(`/admin/users/${e}`,s),delete:e=>r.delete(`/admin/users/${e}`),activate:e=>r.post(`/admin/users/${e}/activate`),deactivate:e=>r.post(`/admin/users/${e}/deactivate`)},auctions:{getAll:e=>r.get("/admin/auctions",{params:e}),getById:e=>r.get(`/admin/auctions/${e}`),approve:e=>r.post(`/admin/auctions/${e}/approve`),reject:(e,s)=>r.post(`/admin/auctions/${e}/reject`,{reason:s}),suspend:(e,s)=>r.post(`/admin/auctions/${e}/suspend`,{reason:s}),delete:e=>r.delete(`/admin/auctions/${e}`)},tenders:{getAll:e=>r.get("/admin/tenders",{params:e}),getById:e=>r.get(`/admin/tenders/${e}`),approve:e=>r.post(`/admin/tenders/${e}/approve`),reject:(e,s)=>r.post(`/admin/tenders/${e}/reject`,{reason:s}),suspend:(e,s)=>r.post(`/admin/tenders/${e}/suspend`,{reason:s}),delete:e=>r.delete(`/admin/tenders/${e}`)},getTender:e=>r.get(`/admin/tenders/${e}`),getTenderSubmissions:(e,s)=>r.get(`/admin/tenders/${e}/submissions`,{params:s}),updateTenderStatus:(e,s)=>r.put(`/admin/tenders/${e}/status`,s),updateTenderSubmissionStatus:(e,s,t)=>r.put(`/admin/tenders/${e}/submissions/${s}/status`,t),reports:{getFinancialReport:e=>r.get("/admin/reports/financial",{params:e}),getUserReport:e=>r.get("/admin/reports/users",{params:e}),getActivityReport:e=>r.get("/admin/reports/activity",{params:e}),getAuctionReport:e=>r.get("/admin/reports/auctions",{params:e}),getTenderReport:e=>r.get("/admin/reports/tenders",{params:e})},settings:{getAll:()=>r.get("/admin/settings"),update:e=>r.put("/admin/settings",e),backup:()=>r.post("/admin/settings/backup"),restore:e=>r.post(`/admin/settings/restore/${e}`)}},d=r},24230:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},67427:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},43727:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},88307:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},53468:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},3634:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},49844:(e,s,t)=>{"use strict";t.d(s,{v:()=>n});var r=t(17577),a=t(41369),i=t(86725),l=["axis","item"],n=(0,r.forwardRef)((e,s)=>r.createElement(i.R,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:a.NL,categoricalChartProps:e,ref:s}))},79385:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[2116,4099,2299,1307,2511,5540,7817],()=>t(1464));module.exports=r})();